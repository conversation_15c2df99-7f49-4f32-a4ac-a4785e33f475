package com.ecco.service;

import java.util.Collection;
import java.util.List;

import javax.annotation.PostConstruct;

import com.ecco.dom.*;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import org.springframework.context.ApplicationContext;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PostFilter;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.security.dto.AclExtractor;
import com.ecco.security.acl.AclHandler;
import lombok.AllArgsConstructor;

@org.springframework.stereotype.Service("entityRestrictionService")
@WriteableTransaction
@AllArgsConstructor
public class EntityRestrictionServiceImpl implements EntityRestrictionService {

    private final AclHandler aclHandler;
    private final ServiceRepository serviceRepository;
    private final ProjectRepository projectRepository;
    private final ApplicationContext applicationContext;

    /**
     * Override the default method to use self-proxy pattern to ensure @PostFilter annotations work
     * when called from within the same class. This solves the Spring AOP limitation where
     * self-invocation bypasses proxy-based aspects.
     */
    @Override
    public ServicesProjectsDto getRestrictedServicesProjectsDto(
            RepositoryBasedServiceCategorisationService svcCatsService) {
        // Get the proxied version of this service to ensure @PostFilter annotations are applied
        EntityRestrictionService proxiedService = applicationContext.getBean(EntityRestrictionService.class);

        // Now call the @PostFilter methods through the proxy
        var restrictedProjects = proxiedService.getRestrictedProjectIds();
        var restrictedServices = proxiedService.getRestrictedServiceIds();
        var svcCatsAll = svcCatsService.getServiceCategorisationViewModels();
        return new ServicesProjectsDto(restrictedServices, restrictedProjects, svcCatsAll);
    }

    /**
     * Instance method to verify access for a service recipient.
     */
    @Override
    public void verifyAccess(int srId, long serviceId, Long projectId, RepositoryBasedServiceCategorisationService svcCatService) {
        ServicesProjectsDto restrictions = getRestrictedServicesProjectsDto(svcCatService);
        // var access = projectId == null ? restrictions.canAccess(serviceId) : restrictions.canAccess(serviceId, projectId);
        if (restrictions.canAccess(serviceId, projectId)) {
            return; // full access to this referral
        }
        // DON'T check the parentReferral anymore, by now the user will need permission for both anyway
        throw new AccessDeniedException("access denied for entity id: " + srId);
    }

    @PostConstruct
    @Override
    public void ensureAcls() {
        Collection<AclExtractor<? extends Identified<Long>>> aclExtractors = aclHandler.aclExtractors();
        for (AclExtractor<? extends Identified<Long>> aclExtractor : aclExtractors) {
            List<? extends Identified<Long>> objects = aclExtractor.listObjects();
            for (Identified<Long> o : objects) {
                if (!aclHandler.checkAclExists(aclExtractor.getClazz().getCanonicalName(), o.getId())) {
                    aclHandler.createAcl(aclExtractor.getClazz().getCanonicalName(), o.getId());
                }
            }
        }
    }

    //@PostFilter("hasPermission(#user, 'allow without acls') or (hasPermission(filterObject, 'read') or hasPermission(filterObject, 'admin'))")
    @PostFilter("hasRole('AAA') or hasPermission(filterObject, 'read')")
    @Override
    public List<ServiceAclId> getRestrictedServiceIds() {
        // not entity-related else we get proxy issues mismatching hasPermission
        return serviceRepository.findAllAclIds();
    }

    @PostFilter("hasRole('AAA') or hasPermission(filterObject, 'read')")
    @Override
    public List<ProjectAclId> getRestrictedProjectIds() {
        // not entity-related else we get proxy issues mismatching hasPermission
        List<ProjectAclId> entities = projectRepository.findAllAclIds();

        // previously, restricting the projects list couldn't help with knowing access to 'any project' (including 'none') or specifically 'this set'
        // so we used add a dummy -1 to indicate 'access all areas' and allows access to 'null' projects
        // this has now been deprecated in favour of simply specifing access at the service level and specifying no projects
        // we can do this because we are no longer assuming that services and project lists are independent of each other
        // as we move to a system where we specify the service with projects explicitly
        // the admin screen has now brought back the -1 permission, so we want to honour it
        // until we get Service and ServiceProject restrictions
        entities.add(ProjectAclId.accessAllProjectsFakeProject);

        // -2 indicates we can show null-assigned referrals in the list, but at present this is not implemented
        //entities.add(Project.accessNullProjectsFakeProject);

        return entities;
    }

}
