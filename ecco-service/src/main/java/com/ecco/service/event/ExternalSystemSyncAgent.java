package com.ecco.service.event;

import com.ecco.config.dom.ExternalSystem;
import com.ecco.config.repositories.ExternalSystemRepository;
import com.ecco.dao.ClientRepository;
import com.ecco.dom.Service;
import com.ecco.dom.servicerecipients.BaseServiceRecipient;
import com.ecco.dom.ClientDetail;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.dto.ClientDefinition;
import com.ecco.dto.ClientDefinitionCommandDto;
import com.ecco.dto.ClientEvent;
import com.ecco.dto.FlagsDefinitionCommandDto;
import com.ecco.event.ClientCreatedEvent;
import com.ecco.evidence.event.EvidenceCommentSavedEvent;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.service.ClientSyncStrategy;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Collection;

import static com.ecco.dom.ClientDetail.toClientDefinition;


@RequiredArgsConstructor
@Slf4j
public class ExternalSystemSyncAgent {

    private final ExternalSystemRepository externalSystemRepository;

    private final MessageBus<ApplicationEvent> messageBus;

    private final Collection<ClientSyncStrategy> delegates;

    private final ClientRepository clientRepository;

    private final ServiceRecipientRepository serviceRecipientRepository;

    private final ServiceRepository serviceRepository;

    @PostConstruct
    protected void init() {
        messageBus.subscribe(ClientCreatedEvent.class, this::syncCreatedClientToExternalSystems);
        messageBus.subscribe(EvidenceCommentSavedEvent.class, this::syncEvidenceToExternalSystems);
    }

    // NB This is called from SyncToExternalSystemAgent - from the flags audit
    // See CommandCreatedEvent
    public void syncFlagsToExternalSystems(FlagsDefinitionCommandDto cmd) {
        log.debug("Starting sync flags for externalClientRef {}", cmd.externalClientRef);
        externalSystemRepository.findByClientSinkTrue().forEach(
                sink -> {
                    //log.debug("Starting sync flags for externalClientRef {} (clientSink true)", client.getExternalClientRef());
                    for (ClientSyncStrategy delegate : delegates) {
                        if (delegate.supports(sink.getApiType())) {
                            Boolean transactionSuccess = delegate.syncEvidenceFlags(sink.getName(), sink.getApiType(), sink.getUri(), cmd);
                            log.info("Synced flags to {} for externalClientRef {} with tx {}", sink.getName(), cmd.externalClientRef, transactionSuccess);
                        }
                    }
                }
        );
    }

    private void syncCreatedClientToExternalSystems(ClientCreatedEvent message) {
        var client = message.getClient();
        log.debug("Starting sync client (create) for externalClientRef {}", client.getExternalClientRef());
        externalSystemRepository.findByClientSinkTrue().forEach(
                sink -> {
                    //log.debug("Starting sync (client) create for externalClientRef {} (clientSink true)", client.getExternalClientRef());
                    ClientDefinition clientDef = toClientDefinition(client);
                    for (ClientSyncStrategy delegate : delegates) {
                        if (delegate.supports(sink.getApiType())) {
                            ClientDefinition result = delegate.syncClient(sink.getName(), sink.getApiType(), sink.getUri(), clientDef);
                            clientRepository.updateExternalSystemRef(client.getId(),
                                    sink.getName(), result.getExternalClientRef());
                            log.info("Synced client (create) to {} created externalClientRef {}", sink.getName(), result.getExternalClientRef());
                        }
                    }
                }
        );
    }

    // NB This is called from SyncToExternalSystemAgent - when the client detail audit changes
    // See CommandCreatedEvent
    public void syncUpdatedClientToExternalSystems(ClientDefinitionCommandDto cmd) {
        log.debug("Starting sync client (update) for externalClientRef {}", cmd.externalClientRef);
        externalSystemRepository.findByClientSinkTrue().forEach(
                sink -> {
                    //log.debug("Starting sync client (update) for externalClientRef {} (clientSink true)", client.getExternalClientRef());
                    for (ClientSyncStrategy delegate : delegates) {
                        if (delegate.supports(sink.getApiType())) {
                            String transactionReference = delegate.syncClientUpdate(sink.getName(), sink.getApiType(), sink.getUri(), cmd);
                            log.info("Synced client (update) to {} for externalClientRef {} with tx {}", sink.getName(), cmd.externalClientRef, transactionReference);
                        }
                    }
                }
        );
    }

    private void syncEvidenceToExternalSystems(EvidenceCommentSavedEvent event) {
        log.debug("Starting sync evidence comment for sr-id {}", event.getServiceRecipientId());

        BaseServiceRecipient serviceRecipient = serviceRecipientRepository.findById(event.getServiceRecipientId())
                .orElse(null);
        ClientDetail client = serviceRecipient instanceof ReferralServiceRecipient
                ? ((ReferralServiceRecipient) serviceRecipient).getReferral().getClient()
                : null;

        if (client == null || client.getExternalClientRef() == null) {
            return; // don't do anything unless we have a client that is sync'd
        }

        log.debug("Starting sync evidence comment for externalClientRef {}", client.getExternalClientRef());

        var skip = serviceRecipient.getParamsOfService("externalNoteSkip");
        if (Boolean.TRUE.toString().equalsIgnoreCase(skip)) {
            log.debug("Stopping sync evidence comment for externalClientRef {} - excluded service", client.getExternalClientRef());
            return;
        }

        String noteTypeStr = serviceRecipient.getParamsOfService(Service.PARAM_INTEGRATION_NOTETYPE);
        ClientEvent.NoteType noteType = StringUtils.hasText(noteTypeStr)
            ? ClientEvent.NoteType.valueOf(noteTypeStr)
            : ClientEvent.NoteType.Contact;

        externalSystemRepository.findByClientSinkTrue().forEach(
                sink -> {
                    //log.debug("Starting sync evidence comment for externalClientRef {} (clientSink true)", client.getExternalClientRef());
                    for (ClientSyncStrategy delegate : delegates) {
                        if (delegate.supports(sink.getApiType())) {
                            //log.debug("Starting sync evidence comment for externalClientRef {} (delegate supported)", client.getExternalClientRef());
                            sendEvidence(event, noteType, client, sink, delegate);
                        }
                    }
                }
        );
    }

    private void sendEvidence(EvidenceCommentSavedEvent event, ClientEvent.NoteType noteType, ClientDetail clientDetail, ExternalSystem sink,
                              ClientSyncStrategy delegate) {

        ClientDefinition client = toClientDefinition(clientDetail);
        //log.debug("Starting sync evidence comment for externalClientRef {} (sendEvidence)", client);
        String transactionReference = delegate.syncEvidenceComment(sink.getName(), sink.getApiType(), sink.getUri(), client, event, noteType);
        log.info("Synced evidence comment to {} for externalClientRef {} with tx {}", sink.getName(), clientDetail.getExternalClientRef(), transactionReference);
    }
}
