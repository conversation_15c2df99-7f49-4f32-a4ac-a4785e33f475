package com.ecco.service.upload;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.dom.upload.UploadedBytes;
import com.ecco.dom.upload.UploadedFile;
import com.ecco.upload.dao.SimpleUploadedFileRepository;
import com.ecco.web.upload.UploadConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@WriteableTransaction
public class UploadServiceImpl implements UploadService {

    @Autowired
    SimpleUploadedFileRepository uploadedFileRepository;

    @Override
    public UploadedFile getSimpleFile(long fileId) {
        UploadedFile uploadedFile = uploadedFileRepository.findFileWithContent(fileId);
        int bytesSize = uploadedFile.getUploadedBytes().getBytes().length;
        return uploadedFile;
    }

    @Override
    public UploadedFile getFile(long fileId, UploadConfig config) {
        return config.getRepository().findFileWithContent(fileId);
    }

    // get the raw bytes (without filename etc)
    @Override
    public UploadedBytes getBytes(long byteId) {
        return uploadedFileRepository.findOneByUploadedBytes_Id(byteId).getUploadedBytes();
    }

    @Override
    public boolean deleteFile(long fileId, UploadConfig config) {
        config.getRepository().deleteById(fileId);
        return true;
    }

    @Override
    public UploadedFile processFile(UploadConfig config) {
        // construct the attachment
        final UploadedFile attachment;
        try {
            attachment = config.constructAttachment();
        } catch (IOException e) {
            return null;
        }

        return config.getRepository().save(attachment);
    }

}

/*
http://www.mkyong.com/hibernate/hibernate-save-image-into-database/
http://stackoverflow.com/questions/2112615/persisting-large-files-with-hibernate-annotations

http://snehaprashant.blogspot.com/2008/08/how-to-store-and-retrieve-blob-object.html

Create UploadFile(FILE_NAME` VARCHAR(1000) NOT NULL,
 `FILE` BLOB NOT NULL);
longblob in mysql
// using byte array not blob - http://forum.springsource.org/showthread.php?t=13663&page=2
// blob copes with larger files (gets saved to global file in db directory?)

File file = new File("/3030.jpg");
FileInputStream fis = new FileInputStream(file);
LobHelper lh = HibernateUtil.getSession().getLobHelper();
Blob b = lh.createBlob(fis, fis.available());
resultExtra.setFile(b);

try {
  byte[] fileInBytes=item.get();
  FileInputStream fileInputStream = new FileInputStream(uploadedFile);
  //convert file into array of bytes
  fileInputStream.read(fileInBytes);
  fileInputStream.close();
} catch (Exception e) {
  e.printStackTrace();
}

UploadedFile object= new UploadedFile();
object.setFile(fileInBytes);
uploadedObject.setFileName(fileName);
session.save(object);
*/
