package com.ecco.service.security;

import com.ecco.dom.Individual;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;

import java.util.Collection;
import java.util.List;
import java.util.UUID;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ecco.security.SecurityUtil.authenticatedUserExists;
import static java.util.Collections.singletonList;

public class RunAsTemplate {
    public static final String FAKE_PASSWORD = UUID.randomUUID().toString();
    public static final List<SimpleGrantedAuthority> EXTERNAL_AUTHORITIES = singletonList(new SimpleGrantedAuthority("ROLE_STAFF"));
    private final Authentication auth;

    /**
     * If no authenticated user exists, then run as "external" for the supplied callback
     */
    public static <T> T runAsExternalUserAccountIfNecessary(Supplier<T> callback) {
        if (authenticatedUserExists()) {
            return callback.get();
        }

        com.ecco.security.dom.User user = new com.ecco.security.dom.User() {
            @Override
            public Collection<? extends GrantedAuthority> getAuthorities() {
                return EXTERNAL_AUTHORITIES;
            }
        };
        user.setNewPassword(FAKE_PASSWORD);
        user.setUsername("ecco_external");
        user.setId(3L);
        Individual i = new Individual();
        i.setId(3L);
        user.setContact(i);
        Authentication auth = new PreAuthenticatedAuthenticationToken(user, null, user.getAuthorities());
        auth.setAuthenticated(true);
        T result = new RunAsTemplate(auth).execute(callback);
        auth.setAuthenticated(false); // As safety for possible leakage
        return result;
    }

    public RunAsTemplate(String... roles) {
        this(new PreAuthenticatedAuthenticationToken(null, null, Stream.of(roles).map(SimpleGrantedAuthority::new).collect(Collectors.toList())));
    }

    public RunAsTemplate(UserDetails user) {
        this(new PreAuthenticatedAuthenticationToken(user, user.getPassword(), user.getAuthorities()));
    }

    public RunAsTemplate(Authentication auth) {
        this.auth = auth;
    }

    public <T> T execute(Supplier<T> callback) {
        final Authentication savedAuthentication = SecurityContextHolder.getContext().getAuthentication();
        try {
            SecurityContextHolder.getContext().setAuthentication(auth);
            return callback.get();
        } finally {
            SecurityContextHolder.getContext().setAuthentication(savedAuthentication);
        }
    }
}
