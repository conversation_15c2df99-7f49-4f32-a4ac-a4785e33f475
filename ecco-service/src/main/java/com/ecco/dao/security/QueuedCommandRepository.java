package com.ecco.dao.security;

import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.security.dom.QueuedCommand;
import org.joda.time.DateTime;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

public interface QueuedCommandRepository extends CrudRepository<QueuedCommand, Long> {

    long countByExecuted(boolean isExecuted);

    @WriteableTransaction
    @Modifying
    @Query("DELETE FROM QueuedCommand qc " +
            "WHERE qc.executed = true " +
            "AND qc.executeTime < :threshold")
    void deleteExecutedEarlierThan(@Param("threshold") DateTime threshold);

    @WriteableTransaction
    @Modifying
    @Query("DELETE FROM QueuedCommand qc " +
            "WHERE qc.executed = true " +
            "AND qc.id + :count < (SELECT max(qc2.id) from QueuedCommand qc2)")
    // FIXME: You can't specify target table 'commandqueue' for update in FROM clause
    void deleteExecutedExceptMostRecent(@Param("count") long count);
}
