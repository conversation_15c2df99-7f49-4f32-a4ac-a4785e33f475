package com.ecco.dao.security;

import com.ecco.security.dom.UserDevice;
import org.springframework.data.repository.CrudRepository;

import java.util.UUID;

//@Transactional(propagation = Propagation.MANDATORY) // TODO: Cannot do this for some reason.
public interface UserDeviceRepository extends CrudRepository<UserDevice, Long> {
    UserDevice findOneByGuid(UUID guid);

    UserDevice findOneByGuidAndValid(UUID guid, boolean valid);
}
