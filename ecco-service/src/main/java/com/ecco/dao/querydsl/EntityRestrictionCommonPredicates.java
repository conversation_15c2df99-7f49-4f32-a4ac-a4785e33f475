package com.ecco.dao.querydsl;

import com.ecco.dom.Project;
import com.ecco.dom.ProjectAclId;
import com.ecco.dom.servicerecipients.QBaseServiceRecipient;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.dom.Service;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.dom.QServiceCategorisation;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Simple placeholder to keep control of how to handle 'all' in reports where 'all' means different things with ACLs
 */
public class EntityRestrictionCommonPredicates {

    private ServicesProjectsDto restrictions;
    private Service selectedService = null;
    private Project selectedProject= null;

    protected EntityRestrictionCommonPredicates(ServicesProjectsDto restrictions, Long selectedServiceId, Long selectedProjectId) {
        this.restrictions = restrictions;
        if (selectedServiceId != null && selectedServiceId != -1) {
            selectedService = new Service(selectedServiceId);
        }
        if (selectedProjectId != null && selectedProjectId != -1) {
            selectedProject = new Project(selectedProjectId);
        }

        // ensure the 'filtersOnProject' is set by running through the logic
        ensureFiltersOnProject();
    }

    private void ensureFiltersOnProject() {
        getServiceCategorisations(QBaseServiceRecipient.baseServiceRecipient.serviceAllocation);
    }

    Predicate getServiceCategorisations(QServiceCategorisation serviceAllocation) {

        // currently, the client is attached to one project - we don't track the history
        // in fact the dom structure exists to do this, we just need to fix the ui to allow a 'transfer' button not just save
        // and then we'd need to alter this project to restrict the project they attended with the date range given
        // the idea this retains the exact movements (think accommodation) regardless of other aspects which may change

        // for clarity, we enforce what we currently have - project selection must exist with a service selection
        BooleanExpression serviceCriteria = null;
        BooleanExpression projectCriteria = null;
        Predicate serviceProjectCriteria = null;

        if (selectedService != null) {

            // check we are allowed the selected service, else give no results
            boolean allowedSelectedService = restrictions == null ||
                        restrictions.getRestrictedServiceIds().stream().anyMatch(svc -> Objects.equals(svc, selectedService.getId()));
            serviceCriteria = allowedSelectedService
                    ? serviceAllocation.service.id.eq(selectedService.getId())
                    : serviceAllocation.service.in(new ArrayList<>()); // dummy

            // check we are allowed the selected project, else give no results
            if (allowedSelectedService && selectedProject != null) {
                boolean allowedSelectedProject = restrictions == null ||
                        restrictions.getServiceRestrictedProjectIds(selectedService.getId()).stream().anyMatch(prj -> Objects.equals(prj.getId(), selectedProject.getId()));
                projectCriteria = allowedSelectedProject
                        ? serviceAllocation.project.id.eq(selectedProject.getId())
                        : serviceAllocation.project.in(new ArrayList<>()); // dummy

            } else {
                // if the service has projects then we want to restrict to those allowed to see
                if (restrictions != null && restrictions.serviceHasProjects(selectedService.getId())
                        && restrictions.hasProjectRestrictions()) {
                    // we assume the more specific project chosen (above) takes priority over the general restricted ones
                    // because if they have got to that project from the ui - let it through
                    // so if there is nothing chosen above, then we are showing 'all' which is when the restrictions kick in
                    var restrictedProjects = restrictions.getServiceRestrictedProjectIds(selectedService.getId());
                    projectCriteria = serviceAllocation.project.id.in(restrictedProjects.stream().map(ProjectAclId::getId).toList());
                }
            }
        } else {
            // if there is no service chosen above, then we are showing 'all' which is when the restrictions kick in
            if (restrictions != null) {

                // no project restrictions are in use, so simply use the services
                if (!restrictions.hasProjectRestrictions()) {
                    // restrict by services - linear (not combined with projects yet)
                    serviceCriteria = serviceAllocation.service.id.in(restrictions.getRestrictedServiceIds());

                // restrict by service/projects
                } else {

                    // for each restricted service, find the service/projects, and create a restriction per combination
                    for (var restrictedServiceId : restrictions.getRestrictedServiceIds()) {
                        // its perfectly possible for the service to have no projects and so needs to be allowed
                        // or for the restrictedProjects to be an empty set where we shouldn't give access (to be safe)
                        // (as was the case with ReferralListFilterSecurityTest.test_filter_restrictToServiceWithUnrelatedProjects)
                        // so we need to specifically specify no access as doing nothing will pass over the method applying no restrictions, giving access to everything

                        // we capture here the service with no projects
                        if (!restrictions.serviceHasProjects(restrictedServiceId)) {
                            serviceProjectCriteria = buildRestrictionServiceTerm(serviceAllocation, serviceProjectCriteria, restrictedServiceId);
                        } else if (restrictions.hasNoProjectRestrictions(restrictedServiceId)) {
                            // create an impossible match for this service
                            serviceProjectCriteria = buildRestrictionServiceProjectTerm(serviceAllocation, serviceProjectCriteria, restrictedServiceId, null);
                        } else {
                            // build per service/project
                            for (var restrictedProjectId : restrictions.getProjectRestrictionIds(restrictedServiceId)) {
                                serviceProjectCriteria = buildRestrictionServiceProjectTerm(serviceAllocation, serviceProjectCriteria, restrictedServiceId, restrictedProjectId);
                            }
                        }
                    }
                }
            }
        }

        Predicate restrictionCriteria = serviceProjectCriteria;
        if (serviceProjectCriteria == null) {
            restrictionCriteria = serviceCriteria;
            if (projectCriteria != null) {
                restrictionCriteria = ExpressionUtils.allOf(serviceCriteria, projectCriteria);
            }
        }

        return restrictionCriteria;
    }

    private Predicate buildRestrictionServiceTerm(QServiceCategorisation serviceAllocation, Predicate serviceProjectCriteria, long restrictedServiceId) {
        Assert.notNull(serviceAllocation.service, "the query is too nested - consider QueryInit or a join");
        BooleanExpression srv = serviceAllocation.service.id.eq(restrictedServiceId);
        if (serviceProjectCriteria == null) {
            serviceProjectCriteria = srv;
        } else {
            serviceProjectCriteria = ExpressionUtils.anyOf(serviceProjectCriteria, srv);
        }

        return serviceProjectCriteria;
    }

    private Predicate buildRestrictionServiceProjectTerm(QServiceCategorisation serviceAllocation, Predicate serviceProjectCriteria, long restrictedServiceId, Long restrictedProjectId) {
        BooleanExpression srv = serviceAllocation.service.id.eq(restrictedServiceId);
        BooleanExpression prj;
        if (restrictedProjectId == null) {
            List<Project> dummy = new ArrayList<>();
            prj = serviceAllocation.project.in(dummy); // 'in' empty list is impossible
        } else {
            prj = serviceAllocation.project.id.eq(restrictedProjectId);
        }
        Predicate srvPrj = ExpressionUtils.allOf(srv, prj);
        if (serviceProjectCriteria == null) {
            serviceProjectCriteria = srvPrj;
        } else {
            serviceProjectCriteria = ExpressionUtils.anyOf(serviceProjectCriteria, srvPrj);
        }
        return serviceProjectCriteria;
    }

    public static Predicate applySecurityPredicate(QBaseServiceRecipient qSR, EntityRestrictionService restrictionService, RepositoryBasedServiceCategorisationService serviceCategorisationService) {
        return EntityRestrictionCommonPredicates.applySecurityPredicate(qSR.serviceAllocation, null, restrictionService, serviceCategorisationService);
    }

    public static Predicate applySecurityPredicate(QBaseServiceRecipient qSR, ReportCriteriaDto dto,
                                                   EntityRestrictionService restrictionService, RepositoryBasedServiceCategorisationService serviceCategorisationService) {
        return EntityRestrictionCommonPredicates.applySecurityPredicate(qSR.serviceAllocation, dto, restrictionService, serviceCategorisationService);
    }

    public static Predicate applySecurityPredicate(QServiceCategorisation qSC, ReportCriteriaDto dto,
                                                   EntityRestrictionService restrictionService,
                                                   RepositoryBasedServiceCategorisationService svcCatsService) {
        Long serviceId = dto != null ? dto.getServiceId() : null;
        Long projectId = dto != null ? dto.getProjectId() : null;

        ServicesProjectsDto restrictions = restrictionService.getRestrictedServicesProjectsDto(svcCatsService);

        EntityRestrictionCommonPredicates terms = new EntityRestrictionCommonPredicates(restrictions, serviceId, projectId);
        return terms.getServiceCategorisations(qSC);
    }

}
