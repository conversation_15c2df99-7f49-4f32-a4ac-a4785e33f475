package com.ecco.dao;

import com.ecco.dom.EvidenceThreatComment;
import org.springframework.data.repository.Repository;

import java.util.UUID;

public interface ThreatCommentRepository extends Repository<EvidenceThreatComment, UUID> {

    EvidenceThreatComment save(EvidenceThreatComment entity);

    EvidenceThreatComment findOneByWork_Id(UUID workUuid);

    void delete(EvidenceThreatComment comment);

}
