package com.ecco.dao;

import com.ecco.dom.Agency;
import com.ecco.dom.Individual;
import com.ecco.serviceConfig.dom.ContactAllocation;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface ContactAllocationRepository extends CrudRepository<ContactAllocation, ContactAllocation.CompositeKey> {

    List<ContactAllocation> findAllByContactId(long contactId);

    @Query("from Agency a inner join ContactAllocation ca on ca.contactId=a.id " +
            "and a.archived is null where ca.serviceAllocationId in (?1) " +
            "ORDER BY a.companyName")
    List<Agency> findAllAgenciesByArchivedIsNullOrderByCompanyNameForAllocationIds(List<Integer> allocationIds);

    @Query("from Individual c inner join ContactAllocation ca on ca.contactId=c.id " +
            "and c.archived is null where ca.serviceAllocationId in (?1) " +
            "ORDER BY c.lastName")
    List<Individual> findAllIndividualsByArchivedIsNullOrderByLastNameForAllocationIds(List<Integer> allocationIds);
}
