package com.ecco.dao;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.ecco.dom.BaseWorkSummary;
import com.ecco.dom.ContactImpl;
import com.querydsl.core.annotations.QueryProjection;

import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.Instant;

/** Represents just a summary of questionnaire work without pulling in all the eager relationships. Used by the web-api. */
@Getter
@Setter
public class QuestionnaireWorkSummary extends BaseWorkSummary {

    private Set<EvidenceQuestionAnswerSummary> answers;
    private List<EvidenceFlagSummary> flags;

    // for cglib
    QuestionnaireWorkSummary() {
        super(null, null,null, null, null, null, null, null, null, null, null, null);
    }

    @QueryProjection
    public QuestionnaireWorkSummary(UUID id, Instant requestedDelete, Long taskDefId, Integer serviceRecipientId,
                                    Integer serviceAllocationId,
                                    ContactImpl author, String comment,
                                    UUID signatureId, DateTime workDate, DateTime createdDate) {
        super(id, requestedDelete, taskDefId, serviceRecipientId, serviceAllocationId,
                author, comment, null, null, signatureId,
                workDate, createdDate);
    }

}
