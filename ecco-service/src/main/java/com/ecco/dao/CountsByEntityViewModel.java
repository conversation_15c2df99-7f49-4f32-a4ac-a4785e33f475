package com.ecco.dao;

import com.querydsl.core.annotations.QueryProjection;

import org.jspecify.annotations.Nullable;


public class CountsByEntityViewModel {

    @Nullable
    public Number entityId;

    public String entityName;

    public Integer count;


    public CountsByEntityViewModel() {
    }

    /**
     * @param count count of the number of items in that date range
     */
    @QueryProjection
    public CountsByEntityViewModel(Integer count) {
        this(null, null, count);
    }

    /**
     * @param entityId an entity that this relates to such as serviceId where grouping by service and date
     * @param entityName the name of the entity being grouped, e.g. service.name
     * @param count count of the number of items in that date range
     */
    @QueryProjection
    public CountsByEntityViewModel(Number entityId, String entityName, Integer count) {
        this.entityId = entityId;
        this.entityName = entityName;
        this.count = count;
    }
}
