package com.ecco.dao;

import com.ecco.infrastructure.spring.data.QueryModifier;
import com.querydsl.core.types.Predicate;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.querydsl.QPageRequest;

import javax.persistence.QueryHint;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface QuestionnaireWorkRepositoryCustom {

    Optional<QuestionnaireWorkSummary> findOneQuestionnaireWorkSummary(UUID workUuid);

    /** Find all the questionnaire work (and answers) of a predicate */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<QuestionnaireWorkSummary> findAllServiceRecipientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(
            Predicate p, QPageRequest page,
            QueryModifier<QuestionnaireWorkSummary> queryModifier);

    /** Find all the questionnaire work (and answers) of a serviceRecipientId */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<QuestionnaireWorkSummary> findServiceRecipientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(int serviceRecipientId, Long evidenceGroupId, boolean attachmentsOnly);

    /** Find all the questionnaire work (and answers) of a clientId */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<QuestionnaireWorkSummary> findClientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(int clientId, long evidenceGroupId);

}
