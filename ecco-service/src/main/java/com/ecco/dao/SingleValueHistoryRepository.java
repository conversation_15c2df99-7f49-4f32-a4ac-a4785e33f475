package com.ecco.dao;

import com.ecco.contacts.dao.HistoryItemRepository;
import com.ecco.dom.SingleValueHistory;

import java.util.List;

public interface SingleValueHistoryRepository extends HistoryItemRepository<SingleValueHistory> {

    List<SingleValueHistory> findByServiceRecipientIdAndKeyOrderByValidFromDesc(int serviceRecipientId, String key);

    List<SingleValueHistory> findByServiceRecipientIdOrderByKeyAscValidFromDesc(int serviceRecipientId);
}
