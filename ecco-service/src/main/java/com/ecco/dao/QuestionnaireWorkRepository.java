package com.ecco.dao;

import com.ecco.dom.EvidenceSupportWork;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.Repository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface QuestionnaireWorkRepository extends Repository<EvidenceSupportWork, UUID>, QuestionnaireWorkRepositoryCustom, QuerydslPredicateExecutor<EvidenceSupportWork> {

    EvidenceSupportWork findById(UUID workUuid);

    @Modifying
    @Query("UPDATE EvidenceSupportWork w " +
            "SET w.version = w.version + 1, w.signature.id = :signatureId " +
            "WHERE w.id IN :workIds")
    void attachSignature(@Param("workIds") List<UUID> workIds, @Param("signatureId") long signatureId);

    // TODO: Just get created field here so it'll just hit the index on the database
    Optional<EvidenceSupportWork> findFirst1ByServiceRecipientIdOrderByCreatedDesc(int serviceRecipientId);

    void delete(EvidenceSupportWork work);

}
