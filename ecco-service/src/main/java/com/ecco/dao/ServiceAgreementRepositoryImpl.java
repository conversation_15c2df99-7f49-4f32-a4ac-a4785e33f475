package com.ecco.dao;

import com.ecco.dom.agreements.QServiceAgreement;
import com.ecco.dom.agreements.ServiceAgreement;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;

public class ServiceAgreementRepositoryImpl implements ServiceAgreementRepositoryCustom {

    /** A subclass to add id field which gets missed by QDSL code gen */
    private static final class QAgreementOfAppointmentsFixed extends QServiceAgreement {
        private static final long serialVersionUID = 1L;
        public final NumberPath<Long> id = createNumber("id", Long.class); // BUG IN QDSL misses this

        public QAgreementOfAppointmentsFixed(String variable) {
            super(variable);
        }
    }

    private static QAgreementOfAppointmentsFixed agreementApps = new QAgreementOfAppointmentsFixed("agreementOfAppointments");

    @PersistenceContext
    private EntityManager entityManager;


    @Override
    @Transactional
    public List<ServiceAgreement> findAllByServiceRecipientId(int serviceRecipientId) {
        JPQLQuery<ServiceAgreement> query = (JPQLQuery<ServiceAgreement>) new JPAQuery(entityManager)
                .from(agreementApps)
                // removed fetch-join as it results in lots of dupl of same record
//                .leftJoin(agreementApps.appointmentSchedules, demandSchedule)
//                .fetch()
                .orderBy(agreementApps.id.desc()) // agreements order by id
                .where(agreementApps.serviceRecipientId.eq(serviceRecipientId));
        return query.fetch();
    }
}
