package com.ecco.dao;

import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.querydsl.QPageRequest;

import javax.persistence.QueryHint;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

/**
 * Adds custom methods to {@link ThreatWorkRepository}
 */
public interface ThreatWorkRepositoryCustom {
    /**
     * Returns work summaries with the actions set populated, by querying the summaries and actions separately and then
     * munging them together.
     *
     * @param serviceRecipientId the referral id for which to return work summaries
     * @return the list of fully-populated summary objects ordered by workDate DESC, created DESC
     *
     * @see ThreatWorkRepository#findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(int, QPageRequest, boolean attachmentsOnly)
     */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    Slice<ThreatWorkSummary> findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(int serviceRecipientId, QPageRequest pr, boolean attachmentsOnly);
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    Slice<ThreatWorkSummary> findAllThreatWorkSummaryWithActionsOrderbyWorkDateDescCreatedDesc(Set<Integer> siblingServiceRecipientIds, QPageRequest pr, boolean attachmentsOnly);

    Optional<ThreatWorkSummary> findOneThreatWorkSummaryWithActions(Integer serviceRecipientId, UUID uuid);
}
