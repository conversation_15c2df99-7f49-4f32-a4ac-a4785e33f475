package com.ecco.dao;

import com.ecco.dom.ServiceRecipientContact;
import com.ecco.dom.ServiceRecipientContactId;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ServiceRecipientContactRepository extends JpaRepository<ServiceRecipientContact, ServiceRecipientContactId> {

    List<ServiceRecipientContact> findAllById_ServiceRecipientId(int id);
    Optional<ServiceRecipientContact> findOneById(ServiceRecipientContactId id);

}
