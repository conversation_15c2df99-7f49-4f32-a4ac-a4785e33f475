package com.ecco.dao;

import com.ecco.dom.Agency;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface AgencyRepository extends JpaRepository<Agency, Long>, CrudRepositoryWithFindOne<Agency, Long> {

    List<Agency> findAllByCode(String code);

    List<Agency> findAllByCompanyName(String companyName);

    List<Agency> findByCompanyNameStartsWith(String comanyNamePrefix);

    List<Agency> findAllByOrderByCompanyName();
    List<Agency> findAllByArchivedIsNullOrderByCompanyName();
}
