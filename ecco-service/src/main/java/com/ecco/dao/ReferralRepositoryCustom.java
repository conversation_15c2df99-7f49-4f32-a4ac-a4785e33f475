package com.ecco.dao;

import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import org.jspecify.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface ReferralRepositoryCustom {

    ReferralSummary findOneReferralSummary(long referralId);

    /** @return ReferralSummary if srId refers to a Referral, but null if it's a different type */
    @Nullable
    ReferralSummary findOneReferralSummaryByServiceRecipientId(int serviceRecipientId);

    /**
     * Clients from referrals (and their security) is a safer option.
     * But this does mean we are susceptible to duplicates, unless we figure that out too.
     * For now, we've done this client side
     */
    Page<ClientDetailWrapper> findAllClientsFromReferralsPredicate(Predicate p, PageRequest pr);

    List<ReferralSummary> findAllReferralSummaryByClient(long clientId);

    List<ReferralSummary> findAllAsReferralSummary(Predicate p);

    List<ReferralSummary> findAllIncludingHiddenAsReferralSummary(Predicate p);

    /** Fetch specified ReferralSummary views filtered to given predicate and sorted/limited according to
     * {@link PageRequest}
     */
    Page<ReferralSummary> findAllAsReferralSummary(Predicate p, PageRequest pr);


    /** Returns pairs of parentReferralId, child serviceRecipientId */
//    @Query("SELECT p.id, c.serviceRecipient.id " +
//            "FROM Referral c JOIN c.parentReferral p " +
//            "WHERE p.id IN ?1")
    List<Tuple> findAllServiceRecipientIdByParentReferral_Ids(Long[] ids);

}
