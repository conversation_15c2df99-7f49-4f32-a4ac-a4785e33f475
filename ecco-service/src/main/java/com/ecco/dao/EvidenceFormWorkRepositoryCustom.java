package com.ecco.dao;

import com.ecco.dom.EvidenceGroup;
import org.jspecify.annotations.Nullable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.querydsl.QPageRequest;

import javax.persistence.QueryHint;

import java.time.LocalDateTime;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface EvidenceFormWorkRepositoryCustom {

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    Slice<EvidenceFormWorkSummary> findAllEvidenceFormWork(Integer serviceRecipientId, @Nullable EvidenceGroup evidenceGroup,
                                                           QPageRequest pr, boolean findAttachmentsOnly,
                                                           @Nullable LocalDateTime from, @Nullable LocalDateTime to);

}
