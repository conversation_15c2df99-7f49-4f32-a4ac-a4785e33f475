package com.ecco.dao;

import com.ecco.infrastructure.entity.IdNameWithCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;
import java.util.Optional;

@NoRepositoryBean
public interface IdNameCodeAbstractRepository<T extends IdNameWithCode> extends JpaRepository<T, Long> {

    T findOneByName(String name);
    T findOneByCode(String code);
    List<T> findAllByName(String name);

    Optional<T> findById(long id);
}
