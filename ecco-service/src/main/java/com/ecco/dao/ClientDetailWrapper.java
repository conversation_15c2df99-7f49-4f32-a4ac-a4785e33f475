package com.ecco.dao;

import com.ecco.dom.ClientDetail;
import com.querydsl.core.annotations.QueryProjection;

/**
 * A wrapper class to allow a QueryProjection without specifying all the properties of, say, ClientViewModel.
 */
public class ClientDetailWrapper {

    ClientDetail client;

    ClientDetailWrapper() {}

    @QueryProjection
    public ClientDetailWrapper(ClientDetail client) {
        this.client = client;
    }

    public ClientDetail getClient() {
        return client;
    }

    public void setClient(ClientDetail client) {
        this.client = client;
    }
}
