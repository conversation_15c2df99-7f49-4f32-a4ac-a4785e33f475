package com.ecco.dao;

import com.ecco.dom.EvidenceFormSnapshot;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import org.joda.time.DateTime;
import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import javax.persistence.QueryHint;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface EvidenceFormSnapshotRepository extends QueryDslPredicateAndProjectionExecutor<EvidenceFormSnapshot, UUID> {

    Optional<EvidenceFormSnapshot> findByWork_Id(UUID workUuid);

    @Query("SELECT s FROM EvidenceFormSnapshot s WHERE " +
            "s.serviceRecipientId = ?1 AND " +
            "s.work.evidenceGroupKey = ?2 AND " +
            "s.created <= ?3 " +
            "ORDER BY s.created DESC")
    List<EvidenceFormSnapshot> findLatestSnapshotByCreatedAndServiceRecipientIdAndEvidenceTaskGroupKey(
            int serviceRecipientId, @NonNull String evidenceGroupKey, @NonNull DateTime timestamp, @NonNull Pageable pageable);

    // TODO: Tested on limited data locally against UAT
    @Query(nativeQuery = true, value="SELECT snp.* FROM evdnc_form_work work1 " +
            "INNER JOIN (SELECT serviceRecipientId, taskDefId, max(workDate) AS maxDate " +
                "FROM evdnc_form_work " +
                "WHERE serviceRecipientId=?1 " +
                "GROUP BY serviceRecipientId, taskDefId) workj " +
            "ON work1.serviceRecipientId=workj.serviceRecipientId " +
                "AND work1.taskDefId=workj.taskDefId " +
                "AND work1.workDate=workj.maxDate " +
            "INNER JOIN evdnc_form_snapshots snp ON snp.workUuid = work1.uuid " +
            "WHERE work1.serviceRecipientId=?1 ")
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    List<EvidenceFormSnapshot> findLatestSnapshotsPerEvidenceGroupByServiceRecipientId(int serviceRecipientId);

    // as above, but with signature not null
    // could be done with really long method name: Optional<EvidenceFormSnapshot> findFirstByServiceRecipientAndWork_EvidenceTaskGroupKeyAndWork_SignatureIsNotNullAndCreatedLessThanEqualOrderByCreatedDesc(int serviceRecipientId, @Nonnull String evidenceTaskGroupKey, @Nonnull DateTime timestamp);
    @Query("SELECT s FROM EvidenceFormSnapshot s WHERE " +
            "s.serviceRecipientId = ?1 AND " +
            "s.work.evidenceGroupKey = ?2 AND " +
            "s.work.signature is not null AND " +
            "s.created < ?3 " +
            "ORDER BY s.work.workDate DESC, s.created DESC")
    List<EvidenceFormSnapshot> findLatestSignedSnapshotByCreatedAndServiceRecipientIdAndEvidenceTaskGroupKey(
            int serviceRecipientId, @NonNull String evidenceGroupKey, @NonNull DateTime timestamp, @NonNull Pageable pageable);

    String snapshotSlice = "SELECT s FROM EvidenceFormSnapshot s WHERE " +
            "s.serviceRecipientId = ?1 AND " +
            "s.work.evidenceGroupKey = ?2 " +
            "ORDER BY s.work.workDate DESC, s.created DESC";
    @Query(snapshotSlice)
    Slice<EvidenceFormSnapshot> findAllSnapshotsByServiceRecipientIdAndEvidenceGroupKey(int serviceRecipientId, @NonNull String evidenceGroupKey, Pageable pr);

    @Query(snapshotSlice)
    Iterable<EvidenceFormSnapshot> findAllSnapshotsByServiceRecipientIdAndEvidenceGroupKeyAll(int serviceRecipientId, @NonNull String evidenceGroupKey);

}
