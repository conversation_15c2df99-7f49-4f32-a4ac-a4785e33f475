package com.ecco.dao;

import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.joda.time.LocalDate;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

/**
 * This class is ...
 *
 * @since 22/04/2014
 */
public interface ServiceAgreementRepository extends CrudRepositoryWithFindOne<ServiceAgreement, Long>,
        ServiceAgreementRepositoryCustom {

    @Query("select a from ServiceAgreement a"
            + " left join fetch a.appointmentSchedules"
            + " left join fetch a.resourceSchedules"
            + " where a.serviceRecipientId = ?1"
            + " and a.start = ?2"
            + " and a.end is null")
    Optional<ServiceAgreement> findAgreementWithScheduleStartDate(Integer serviceRecipientId, LocalDate start);
}
