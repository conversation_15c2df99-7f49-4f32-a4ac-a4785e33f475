package com.ecco.dao;

import com.ecco.dom.ClientDetail;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ClientRepository extends QueryDslPredicateAndProjectionExecutor<ClientDetail, Long> {
    @Query("select client from ClientDetail client inner join client.referrals referral where referral.id = ?1")
    ClientDetail findOneByReferralId(long referralId);

    @Query("select client.id from ClientDetail client where client.contact.id = ?1")
    Long findOneIdByContactId(long contactId);

    @Query("SELECT client FROM ClientDetail client INNER JOIN client.referrals referral"
            + " WHERE referral.serviceRecipient.id = ?1")
    ClientDetail findOneByServiceRecipientId(int serviceRecipientId);

    @Query("SELECT client FROM ClientDetail client INNER JOIN client.referrals referral"
            + " WHERE referral.serviceRecipient.id IN (?1)")
    List<ClientDetail> findAllByServiceRecipientId(int[] serviceRecipientId);

    @Query("SELECT client FROM ClientDetail client INNER JOIN client.referrals referral"
            + " WHERE client.id IN (?1)")
    List<ClientDetail> findAllById(long[] ids);

    Optional<ClientDetail> findOneByContact_Id(long contactId);

    List<ClientDetail> findAllByContact_Email(String email);

    List<ClientDetail> findAllByCode(String code);

    Optional<ClientDetail> findFirstByNiIgnoreCaseOrderByCreatedDesc(String id);

    List<ClientDetail> findAllByExternalClientRef(String code);

    ClientDetail findOneByExternalClientSource_NameAndExternalClientRef(String externalClientSourceName, String externalClientRef);

    /** Update client with ref, so long as there isn't already a reference */
    @Modifying
    @Query("UPDATE ClientDetail c " +
            "SET c.externalClientSourceName = :externalSourceName, c.externalClientRef = :externalClientRef " +
            "WHERE c.id = :clientId " +
            "AND c.externalClientRef IS NULL")
    void updateExternalSystemRef(@Param("clientId") Long clientId,
                                 @Param("externalSourceName") String externalSourceName,
                                 @Param("externalClientRef") String externalClientRef);

    @Modifying
    @Query("UPDATE ClientDetail SET "
            + "version = version + 1, "
            + "residenceId = :bldgId "
            + "WHERE id = :clientId")
        // https://stackoverflow.com/questions/43873538/how-to-update-the-jpa-hibernate-version-field-in-a-spring-data-jpa-modifying
    void updateResidingAt(@Param("clientId") long clientId, @Param("bldgId") Integer bldgId);
}
