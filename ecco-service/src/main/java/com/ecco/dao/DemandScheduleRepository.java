package com.ecco.dao;

import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import org.joda.time.LocalDate;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import javax.persistence.QueryHint;
import java.util.List;
import java.util.Optional;

import static org.hibernate.jpa.QueryHints.HINT_READONLY;

public interface DemandScheduleRepository extends CrudRepositoryWithFindOne<DemandSchedule, Long>,
        QueryDslPredicateAndProjectionExecutor<DemandSchedule, Long> {
    /**
     * Retrives an appointmentSchedule by its calendar entry handle.
     *
     * @param entryHandleAsString must not be {@literal null}.
     * @return the entity with the given handle or {@literal null} if none found
     * @throws IllegalArgumentException if {@code id} is {@literal null}
    */
    Optional<DemandSchedule> findOneByEntryHandleAsString(String entryHandleAsString);

    /**
     * Find the children for this parent.
     */
    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e.id FROM DemandSchedule e WHERE e.parentScheduleId = ?1")
    List<Long> findIdsByParentScheduleId(Long id);

    @QueryHints(@QueryHint(name = HINT_READONLY, value = "true"))
    @Query("SELECT e.parentScheduleId FROM DemandSchedule e WHERE e.id = ?1")
    Long findParentScheduleIdFromId(Long id);

    /**
     * Retrieves a list of demandSchedules relating to a specific referral.
     *
     * @param serviceRecipientId must not be {@literal null}.
     * @return the entities associated (via {@link ServiceAgreement}.
     */
    List<DemandSchedule> findAllByAgreementServiceRecipientId(int serviceRecipientId);

    @Query("select s from AppointmentSchedule s"
            + " where s.start <= ?2 and (s.end >= ?1 or s.end is null)")
    List<AppointmentSchedule> findAppointmentsByScheduleDates(LocalDate startRange, LocalDate endRange);

}
