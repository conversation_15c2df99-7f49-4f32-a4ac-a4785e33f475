package com.ecco.dao;

import com.ecco.dom.*;
import com.ecco.infrastructure.spring.data.QueryDslJpaEnhancedRepositoryImpl;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.jspecify.annotations.Nullable;
import org.springframework.data.domain.Slice;
import org.springframework.data.querydsl.QPageRequest;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.LocalDateTime;

public class EvidenceFormWorkRepositoryImpl implements EvidenceFormWorkRepositoryCustom {

    @PersistenceContext
    private EntityManager em;

    // see ThreatWorkRepository.findAllThreatWorkSummaryByServiceRecipientIds
    @Override
    public Slice<EvidenceFormWorkSummary> findAllEvidenceFormWork(Integer serviceRecipientId, EvidenceGroup evidenceGroup,
                                                                  QPageRequest pr, boolean findAttachmentsOnly,
                                                                  @Nullable LocalDateTime from, @Nullable LocalDateTime to) {

        QEvidenceFormWork workQ = QEvidenceFormWork.evidenceFormWork;
        QContactImpl authorQ = QContactImpl.contactImpl;
        QEvidenceFormComment commentQ = QEvidenceFormComment.evidenceFormComment;
        QEvidenceFormSnapshot snapshotQ = QEvidenceFormSnapshot.evidenceFormSnapshot;

        // no attachments for custom form evidence
        // otherwise this would be added to where clause
        // BooleanExpression attachmentsOnlyExp = findAttachmentsOnly ?
        // workQ.attachments.isNotEmpty()
        //  : null;

        BooleanExpression evidenceGroupExp = evidenceGroup != null
                ? workQ.evidenceGroupKey.equalsIgnoreCase(evidenceGroup.getName())
                : null;

        BooleanExpression between = null;
        if (from != null && to != null) {
            DateTime fromDt = JodaToJDKAdapters.localDateTimeToJoda(from).toDateTime(DateTimeZone.UTC);
            DateTime toDt = JodaToJDKAdapters.localDateTimeToJoda(to).toDateTime(DateTimeZone.UTC);
            between = workQ.created.between(fromDt, toDt);
        }

        QEvidenceFormWorkSummary resultObj = new QEvidenceFormWorkSummary(
                workQ.id, workQ.requestedDelete, workQ.taskDefId, workQ.serviceRecipient.id,
                workQ.serviceRecipient.serviceAllocation.id,
                authorQ,
                snapshotQ.json, snapshotQ.formDefinitionUuid,
                commentQ.comment, commentQ.typeDefId, commentQ.minutesSpent,
                workQ.signature.id, workQ.workDate, workQ.created);
        JPQLQuery<EvidenceFormWorkSummary> query = new JPAQuery<>(em);
        query.from(workQ)
                .select(resultObj)
                .leftJoin(workQ.author, authorQ)
                .leftJoin(workQ.comment, commentQ)
                .leftJoin(workQ.snapshot, snapshotQ)
                .orderBy(workQ.workDate.desc())
                .orderBy(workQ.created.desc())
                .where(workQ.serviceRecipient.id.in(serviceRecipientId),
                        evidenceGroupExp,
                        between);

        return QueryDslJpaEnhancedRepositoryImpl.queryAsSlice(query, pr, resultObj);
    }

}
