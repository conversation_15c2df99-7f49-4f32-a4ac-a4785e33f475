package com.ecco.dao;

import com.ecco.dom.*;
import com.ecco.infrastructure.spring.data.QueryModifier;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Multimaps;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.querydsl.QPageRequest;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Custom implementation to take advantage of using ..Summary classes which avoid eager pull in of data we
 * don't need.
 */
@SuppressWarnings("unused") // It's used by Spring Data
public class QuestionnaireWorkRepositoryImpl implements QuestionnaireWorkRepositoryCustom {

    @Autowired
    private ReferralRepository referralRepository;

    @Autowired
    private EvidenceQuestionAnswerRepository questionAnswerRepostory;

    @Autowired
    private EvidenceSupportFlagRepository flagRepository;

    @Autowired
    private EvidenceAttachmentRepository evidenceAttachmentRepository;

    @PersistenceContext
    private
    EntityManager em;

    @Override
    public List<QuestionnaireWorkSummary> findServiceRecipientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(int serviceRecipientId, Long evidenceGroupId, boolean attachmentsOnly) {
        return findAllQuestionnaireWorkSummary(Collections.singleton(serviceRecipientId), evidenceGroupId, attachmentsOnly);
    }

    @Override
    public List<QuestionnaireWorkSummary> findClientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(int clientId, long evidenceGroupId) {

        Set<Integer> serviceRecipientIds = referralRepository.findAllServiceRecipientIdsFromClientId(clientId);

        return findAllQuestionnaireWorkSummary(serviceRecipientIds, evidenceGroupId, false);
    }

    @Override
    public List<QuestionnaireWorkSummary> findAllServiceRecipientQuestionnaireWorkSummaryWithAnswersOrderbyWorkDateDescCreatedDesc(
            Predicate p, QPageRequest page,
            QueryModifier<QuestionnaireWorkSummary> queryModifier) {
        final List<QuestionnaireWorkSummary> workSummaries = findAllQuestionnaireWorkSummary(p, page, queryModifier);
        Set<Integer> serviceRecipientsId = workSummaries.stream()
            .map(BaseWorkSummary::getServiceRecipientId)
            .collect(Collectors.toSet());
        populateWithQuestionAnswerSummary(serviceRecipientsId, workSummaries);
        return workSummaries;
    }

    @Override
    public Optional<QuestionnaireWorkSummary> findOneQuestionnaireWorkSummary(UUID workUuid) {
        QQuestionnaireWorkSummary resultObj = createQueryProjection();
        JPQLQuery<EvidenceSupportWork> query = this.createQueryForSummaryOrderbyWorkDateDescCreatedDesc();

        QEvidenceSupportWork workQ = QEvidenceSupportWork.evidenceSupportWork;
        BooleanExpression uuidOnlyExp = workQ.id.eq(workUuid);
        query.where(uuidOnlyExp);
        var work = query.select(resultObj).fetch();

        if (work.size() == 1) {
            final List<EvidenceQuestionAnswerSummary> answers = questionAnswerRepostory.findOneQuestionAnswerSummary(workUuid);
            final List<EvidenceFlagSummary> flags = flagRepository.findOneSupportFlagSummary(workUuid);
            List<EvidenceAttachmentSummary> attachments = evidenceAttachmentRepository.findSupportAttachmentsByWorkUuid(workUuid);
            var w = work.get(0);
            w.setAnswers(ImmutableSet.copyOf(answers));
            w.setFlags(flags);
            w.setAttachments(attachments);
            return Optional.of(w);
        }
        return Optional.empty();
    }

    private List<QuestionnaireWorkSummary> findAllQuestionnaireWorkSummary(Set<Integer> serviceRecipientIds, Long evidenceGroupId, boolean attachmentsOnly) {
        final List<QuestionnaireWorkSummary> workSummaries = findAllQuestionnaireWorkSummaryByServiceRecipientIdsOrderbyWorkDateDescCreatedDesc(serviceRecipientIds, evidenceGroupId, attachmentsOnly);
        populateWithQuestionAnswerSummary(serviceRecipientIds, workSummaries);
        return workSummaries;
    }

    /**
     * Add the actual answers to the QuestionnaireWorkSummary
     */
    private void populateWithQuestionAnswerSummary(final Set<Integer> serviceRecipientIds, final List<QuestionnaireWorkSummary> workSummaries) {
        if (!workSummaries.isEmpty()) {
            // TODO this gets across all time?
            final List<EvidenceQuestionAnswerSummary> answers =
                    questionAnswerRepostory.findAllQuestionAnswerSummaryByWork_serviceRecipientIds(serviceRecipientIds);
            // Map it
            final ImmutableListMultimap<UUID, EvidenceQuestionAnswerSummary> answerMap = Multimaps.index(answers, EvidenceQuestionAnswerSummary::getWorkId);

            // TODO this gets across all time?
            final List<EvidenceFlagSummary> flags =
                    flagRepository.findAllSupportFlagSummaryByServiceRecipientIds(serviceRecipientIds);
            final ImmutableListMultimap<UUID, EvidenceFlagSummary> flagsByWorkId = Multimaps.index(flags, EvidenceFlagSummary::getWorkId);

            // TODO this gets across all time?
            // and again for attachments
            List<EvidenceAttachmentSummary> attachments = evidenceAttachmentRepository.findSupportAttachmentsByServiceRecipientIds(serviceRecipientIds);
            Map<UUID, List<EvidenceAttachmentSummary>> attachmentsMap = attachments.stream()
                    .collect(Collectors.groupingBy(EvidenceAttachmentSummary::getWorkId));

            // Munge them together
            for (QuestionnaireWorkSummary workSummary : workSummaries) {
                UUID key = workSummary.getId();
                workSummary.setAnswers(ImmutableSet.copyOf(answerMap.get(key)));
                workSummary.setFlags(ImmutableList.copyOf(flagsByWorkId.get(key)));
                workSummary.setAttachments(attachmentsMap.get(key));
            }

        }
    }

    private List<QuestionnaireWorkSummary> findAllQuestionnaireWorkSummaryByServiceRecipientIdsOrderbyWorkDateDescCreatedDesc(Set<Integer> serviceRecipientIds, Long evidenceGroupId, boolean attachmentsOnly) {
        QQuestionnaireWorkSummary resultObj = createQueryProjection();
        JPQLQuery<EvidenceSupportWork> query = this.createQueryForSummaryOrderbyWorkDateDescCreatedDesc();

        QEvidenceSupportWork workQ = QEvidenceSupportWork.evidenceSupportWork;
        BooleanExpression attachmentsOnlyExp = attachmentsOnly ?
                workQ.attachments.isNotEmpty()
                : null;

        BooleanExpression evidenceGroupExp = evidenceGroupId != null
                ? workQ.evidenceGroupId.eq(evidenceGroupId)
                : workQ.evidenceGroupId.ne((long) TaskDefinitionNameIdMappings.Task.SUPPORT_PLAN.taskDefinitionId); // questionnaires use the support table, but they should not be '19' evidenceGroupId

        query.where( workQ.serviceRecipient.id.in(serviceRecipientIds),
                evidenceGroupExp,
                attachmentsOnlyExp);

        return query.select(resultObj).fetch();
    }

    /**
     * Find just the work entries (without the answers) in an effort to cut down the number of queries
     */
    private List<QuestionnaireWorkSummary> findAllQuestionnaireWorkSummary(Predicate p, QPageRequest page,
                                                                           QueryModifier<QuestionnaireWorkSummary> queryModifier) {
        QQuestionnaireWorkSummary resultObj = createQueryProjection();
        JPQLQuery<QuestionnaireWorkSummary> baseQuery = this.createQueryForSummaryOrderbyWorkDateDescCreatedDesc().select(resultObj);

        baseQuery.where(p);

        JPQLQuery<QuestionnaireWorkSummary> query = queryModifier != null ? queryModifier.apply(baseQuery) : baseQuery;
        query.limit(page.getPageSize()).offset(page.getOffset());

        return query.fetch();
    }

    private QQuestionnaireWorkSummary createQueryProjection() {
        QEvidenceSupportWork workQ = QEvidenceSupportWork.evidenceSupportWork;
        QEvidenceSupportComment commentQ = QEvidenceSupportComment.evidenceSupportComment;
        QContactImpl authorQ = QContactImpl.contactImpl;

        return new QQuestionnaireWorkSummary(
                workQ.id, workQ.requestedDelete, workQ.taskDefId, workQ.serviceRecipient.id,
                workQ.serviceRecipient.serviceAllocation.id,
                authorQ, commentQ.comment,
                workQ.signature.id, workQ.workDate, workQ.created);
    }

    private JPQLQuery<EvidenceSupportWork> createQueryForSummaryOrderbyWorkDateDescCreatedDesc() {
        QEvidenceSupportWork workQ = QEvidenceSupportWork.evidenceSupportWork;
        QEvidenceSupportComment commentQ = QEvidenceSupportComment.evidenceSupportComment;
        QContactImpl authorQ = QContactImpl.contactImpl;

        // NB its important to use alias in 'leftJoin(source, alias)' since the alias needs to be used
        // in the list(resultObj) to use the left joins, and not use inner joins from the original entity
        // see the test GenericTypeWorkRepositoryTest
        JPQLQuery<EvidenceSupportWork> query = new JPAQuery<>(em);
        query.from(workQ)
                    .leftJoin(workQ.author, authorQ)
                    .leftJoin(workQ.comment, commentQ)
                    .orderBy(workQ.serviceRecipient.id.asc())
                    .orderBy(workQ.workDate.desc())
                    .orderBy(workQ.created.desc());
        return query;
    }

}
