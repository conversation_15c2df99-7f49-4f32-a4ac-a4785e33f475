package com.ecco.dao;

import com.ecco.dom.EvidenceThreatFlag;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

import java.util.List;
import java.util.Set;

public interface ThreatFlagRepository extends Repository<EvidenceThreatFlag, Long> {

    @Query("select new com.ecco.dao.EvidenceFlagSummary(" +
            " f.id, f.flagDefId, f.value, f.work.id)" +
            " from EvidenceThreatFlag f" +
            " where f.serviceRecipient.id in ?1" +
            " order by f.work.workDate DESC"
    )
    List<EvidenceFlagSummary> findAllThreatFlagSummaryByServiceRecipientIds(Set<Integer> serviceRecipientId);

    // TODO change this to be consistent with SupportActionRepository#findLatestByServiceRecipientIdAndActionInstanceUuidAndCreatedLessOrEqualTo
    @Query("SELECT f1 FROM EvidenceThreatFlag f1 WHERE " +
            "f1.serviceRecipient.id in ?1 AND " +
            "f1.id >= all ( " +
                "SELECT f2.id FROM EvidenceThreatFlag f2 WHERE "+
                "f2.serviceRecipient.id in ?1 AND " +
                "f2.flagDefId = f1.flagDefId)")
    List<EvidenceThreatFlag> findLatestFlagsByServiceRecipientIds(Set<Integer> serviceRecipientIds);

}
