package com.ecco.dao;

import com.ecco.dom.ServiceRecipientAttachment;
import com.ecco.upload.dao.UploadedFileRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @since 12/08/2014
 */
public interface ServiceRecipientAttachmentRepository extends UploadedFileRepository<ServiceRecipientAttachment> {

    /**
     * Retrieves a list of all files of a particular type, with a particular parent.
     *
     * @param serviceRecipientId the id of the recipient to filter by, or null if no filtering required
     * @return the list of files
     */
    @Query("from ServiceRecipientAttachment a where a.serviceRecipient.id = ?1")
    List<ServiceRecipientAttachment> findFiles(final int serviceRecipientId);

    @Query("from ServiceRecipientAttachment a where a.id = ?1")
    ServiceRecipientAttachment findOne(int fileId);

    @Override
    @Query("from ServiceRecipientAttachment a join fetch a.uploadedBytes where a.id = ?1")
    ServiceRecipientAttachment findFileWithContent(final Long fileId);

    @Query("from ServiceRecipientAttachment a where a.evidencePageGroup is null order by id asc")
    List<ServiceRecipientAttachment> findAllFloatingFiles();

    @Query(nativeQuery = true, value = "SELECT ra.* FROM svcrec_attachments ra " +
            "LEFT JOIN evdnc_attachments swa ON swa.fileid = ra.id " +
            "WHERE ra.serviceRecipientId = ?1 AND ra.evidencePageGroup = ?2 " +
            "AND swa.fileId is null")
    List<ServiceRecipientAttachment> findAllUnusedByServiceRecipientIdAndEvidencePageGroup(final Integer serviceRecipientId,
                                                                                           final String evidencePageGroup);

    // Native query avoids referencing the same physical table twice through two different entities.
    // But due to defect HHH-2536 / HHH-2225 / JBPAPP-6571, you can't use native queries with @Formula columns.
    @Query(nativeQuery = true, value = "SELECT DISTINCT ra.* FROM svcrec_attachments ra " +
        "JOIN evdnc_attachments swa ON swa.fileid = ra.id " +
        "WHERE ra.serviceRecipientId = ?1 AND swa.workUuid = ?2")
    List<ServiceRecipientAttachment> findAllByServiceRecipientIdAndWorkId(final Integer serviceRecipientId, final String workId);
}
