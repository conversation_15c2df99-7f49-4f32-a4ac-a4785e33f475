package com.ecco.dao;

import com.ecco.dom.EvidenceThreatOutcome;

import java.util.List;
import java.util.Set;
import org.jspecify.annotations.NonNull;
import org.joda.time.DateTime;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

public interface RiskAreaEvidenceRepository extends Repository<EvidenceThreatOutcome, Long> {

    @Query("select new com.ecco.dao.ThreatAreaSummary(" +
            " e.work.id, o.name, o.id," +
            " e.work.id, e.levelMeasure, e.level, e.trigger, e.control)" +
            " from EvidenceThreatOutcome e left join e.outcome o" +
            " where e.work.serviceRecipient.id in ?1" +
            " order by e.work.workDate DESC, e.work.created DESC")
    List<ThreatAreaSummary> findAllThreatAreaSummaryByServiceRecipientIds(Set<Integer> serviceRecipientIds);

    @NonNull
    @Query("SELECT outcome FROM EvidenceThreatOutcome outcome " +
            "WHERE outcome.serviceRecipient.id = ?1 " +
            "    AND outcome.outcome.id = ?2 " +
            "    AND outcome.created <= ?3 " +
            "ORDER BY outcome.created DESC")
    List<EvidenceThreatOutcome> findLatestByServiceRecipientIdAndAreaIdAndTimestamp(int serviceRecipientId,
                                                                                    long areaId, @NonNull DateTime timestamp, @NonNull Pageable pageable);

    @NonNull
    EvidenceThreatOutcome save(@NonNull EvidenceThreatOutcome outcome);
}
