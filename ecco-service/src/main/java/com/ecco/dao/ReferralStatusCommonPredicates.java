package com.ecco.dao;

import com.ecco.dao.querydsl.PredicateSupport;
import com.ecco.dom.QReferral;
import com.ecco.dom.QReferralServiceRecipient;
import com.ecco.dom.ReferralStatusName;
import com.ecco.calendar.core.util.DateTimeUtils;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.springframework.util.Assert;

public class ReferralStatusCommonPredicates {

    // status can change over time, but we want repeatable reports going back in time (ie it was live last month and closed this month, it would not appear in the live figures for any period)
    // previously we utilised boolean 'statuses' which changed over time, and the from and to were simply to capture referral created/received date
    // to move to repeatable reports we must never utilise the boolean/flag 'states' without reference to corresponding dates (eg acceptedOnService) in the reports
    // because we re-use the dates for accepted/rejected, we assume that such a decision was right first time (even if its changed later, the reports reflect a one-time decision only)
    // this is acceptable because this is in fact what a rejection/signpost is for, whereas a close-off is an exit and follows the normal flow
    // so, for now we ensure the 'expected' flow of a referral is repeatable - but any variance, such as accepted then going to rejected assumes a mistake was made - and report changes accordingly
    // referral accepted flag & date -> service accepted flag & date -> closed date (the close date does not change any prior logic - rejecting does)
    // so reports will be repeatable if the normal flow is assumed [eg referral -> accepted -> support -> close: but if we accept then reject, we assume it was a mistake]
    // NB we are assuming a signpost (rejection) is not a suitable 'exit' - as a rejection means 'receivingService' is reversed (see ReferralFlowAction)
    // within the repeatable reports we may only be interested in the new referrals during the date ranges

    private boolean receivedOnly;
    private DateTime toIn;
    private DateTime fromIn;
    private boolean asAt;
    private DateTime toParam;
    private DateTime fromParam;
    private ReferralStatusName status;

    // As per Referral:
    // 'rejected' is not intended to retain any other status over time - as per ReferraulStatusCommonPredicates 'if we accept then reject, we assume it was a mistake'.
    // However, it does not overwrite the acceptedReferral or decisionReferralMadeOn date should that wish to be queried.
    // As above:
    // any variance, such as accepted then going to rejected assumes a mistake was made
    private static final BooleanExpression anyRejected = QReferral.referral.decisionMadeOnDT.isNotNull().and(QReferral.referral.acceptedOnService.isFalse());
    private static final BooleanExpression neverRejected = anyRejected.not();

    public ReferralStatusCommonPredicates(boolean receivedOnly, LocalDate localFrom, LocalDate localTo, ReferralStatusName status) {
        this(receivedOnly,
                localFrom == null ? null : localFrom.toDateTimeAtStartOfDay(DateTimeZone.forID("Europe/London")),
                localTo == null ? null : localTo.toDateTimeAtStartOfDay(DateTimeZone.forID("Europe/London")),
                DateTimeZone.forID("Europe/London"),
                status);
    }

    /**
     *
     * @param receivedOnly Indicates whether the status is limited to those received in the same period
     * @param from The from date in the users timezone
     * @param to The to date in the users timezone
     * @param zone The users zone - so we can create dates in the expected zone
     * @param status The referral status
     */
    private ReferralStatusCommonPredicates(boolean receivedOnly, DateTime from, DateTime to, DateTimeZone zone,
                                           ReferralStatusName status) {
        this.receivedOnly = receivedOnly;
        checkTzAssumptions();
        initStatus(status);
        initDates(to, from, zone);
    }

    /**
     * Bit of a quick hack - we could create a data structure which the rest of the code uses, but we don't want to bed-in
     * any more work no something which should be resolved for good in the db.
     * Here we simply assert what is assumed and coded in this class.
     */
    private void checkTzAssumptions() {
        Assert.isTrue(PredicateSupport.isSystemField(QReferral.referral.serviceRecipient.created));
        Assert.state(!PredicateSupport.isSystemField(QReferral.referral.receivedDate));
        Assert.isTrue(PredicateSupport.isSystemField(QReferral.referral.decisionMadeOnDT));
        Assert.isTrue(PredicateSupport.isSystemField(QReferral.referral.decisionReferralMadeOnDT));
        Assert.state(!PredicateSupport.isSystemField(QReferral.referral.receivingServiceDate));
        Assert.state(!PredicateSupport.isSystemField(QReferral.referral.exited));

    }
    private void initStatus(ReferralStatusName statusIn) {
        // check if null, we have some status
        status = statusIn == null ? ReferralStatusName.Received : statusIn;
    }

    private void initDates(DateTime to, DateTime from, DateTimeZone zone) {

        // it seems users use inclusive dates (ie end of the month)
        // so we add a day so that we can use our exclusive date ranges
        // it contains no time, hence add a day is ok
        DateTime toOrig = to;
        to = to == null ? null : to.plusDays(1);

        // assign these as the sanitised original incoming dates
        this.toIn = to;
        this.fromIn = from;

        // ensure from/to exists (set to 'today') to avoid edge cases (eg an edge case would be: if from/to are null, a 'live' report needs to ensure exited is not null)
        // doing this allows the same logic for 'all now/current' and using a date range
        // if one of the dates is supplied - it is duplicated in the other, which works as an 'as of' date
        // if no dates are supplied then today's date is used, which works as an 'as of' today
        // NB the real dates are still used in the 'receivedCriteria' above - which could be useful in providing more flexibility/power, or just more confusing!
        DateTime toTmp = to;
        DateTime fromTmp = from;
        // one blank date (or both) represents an 'as at' date
        // the reports are designed to work in either scenario (a range or asAt)
        // the asAt boolean is currently only used in the rejected status
        // if both blank, then assume a day's snapshot from 'now' - which is the whole database at this point (exactly - since it will use the time part of 'now')

        DateTime now = new DateTime(zone);
        if (to == null && from == null) {
            fromTmp = now;
            asAt = true;
            // a day from now shouldn't affect the report - its just the reports are easier without checking for nulls
            toTmp = now.plusDays(1);
            // if only 'from' provided then assume to is now
        } else if (to == null) {
            // set to = now
            // but since we have a from, lets use a comparable 'now' which ignores time, since the from has time ignored from the controller (uses style S-)
            DateTime nowMidnight = DateTimeUtils.setTimeToStartOfDay(now);
            // just check we aren't the same day
            asAt = from.isEqual(nowMidnight);
            toTmp = nowMidnight.plusDays(1);
            // if only 'to' provided, then assume a day's snapshot - which is the whole database at this point
        } else if (from == null) {
            // set from = to
            asAt = true;
            fromTmp = to.minusDays(1);
            // check the incoming vars are the same
            // if so, the toTmp date is already set from the to date which has been amended for exclusive data range
        } else if (toOrig.isEqual(from)) {
            asAt = true;
        }

        toParam = toTmp;
        fromParam = fromTmp;
    }

    private Predicate getReceivedDuringPeriodCriteria(QReferral qr) {

        // can be important to see just the 'new' referrals in that period - eg all live in June who were received in June
        // no from/to date implies everything - no restriction
        BooleanExpression fromCriteria = null;
        BooleanExpression toCriteria = null;
        if (receivedOnly) {
            if (fromIn != null) {
                fromCriteria = qr.receivedDate.goe(fudgeUtc(fromIn));
            }
            if (toIn != null) {
                // to has already been made exclusive date range
                toCriteria = qr.receivedDate.lt(fudgeUtc(toIn));
            }
        }

        return ExpressionUtils.allOf(fromCriteria, toCriteria);
    }

    public Predicate getReferralStatus(QReferral qr) {

        Predicate statusCriteria;
        Predicate receivedOnlyDuringPeriodCriteria = null;

        switch (status) {
            case AllNoDates:
                return null;

            case DueSlaTask:
                statusCriteria = qr.serviceRecipient.nextSlaDueDate.isNotNull();
                break;

            // all is now shown as 'created' as it makes no sense to show all referrals when there may be a date range set
            // therefore 'all' is interpreted as created
            // we did interpret as 'received' but not all referrals have a received date, so created captures more 'all' status
            case Created:
                statusCriteria = created(qr.serviceRecipient);
                break;

            // we wanted to avoid 'received date' status since it might miss some referrals who don't have one set
            // however, for historically created date this is the simplest report to use and probably what people want most
            case Received:
                statusCriteria = received(qr);
                break;

            // incomplete
            // shows those having some incomplete status during the period [whether accepted or rejected] at the service level
            // which includes incomplete referral decisions, but also includes completed referral decisions
            // if the referral is accepted - it is still currently deemed incomplete, whereas a rejection is a decision made at the 'service' level
            // !hasMovedIn && !referral.decision && !referral.acceptedPendingInterview
            case Incomplete:
                statusCriteria = incomplete(qr, false);
                receivedOnlyDuringPeriodCriteria = getReceivedDuringPeriodCriteria(qr);
                break;

            // those incomplete at the end of the period
            // which is different to 'incomplete' which is anything that was incomplete at some point during the period
            case IncompleteAtEnd:
                statusCriteria = incomplete(qr, true);
                break;

            // NB could do AcceptedReferral for appropriateReferral but not so useful as this its expected to always be yes

            // accepted during the period, which is those that have gone live during the period
            // which is different to live, since live gets everything that is live during the period (reduced perhaps
            // to those receivedOnlyDuringPeriodCriteria)
            case AcceptedService:
                statusCriteria = accepted(qr);
                break;

            // all those who are 'accepted' on service
            // leave dates blank for 'now'
            case Live:
                statusCriteria = live(qr, false);
                receivedOnlyDuringPeriodCriteria = getReceivedDuringPeriodCriteria(qr);
                break;

            // all those who are 'accepted' on service at the end of the period
            case LiveAtEnd:
                statusCriteria = live(qr, true);
                break;

            // movein - an accommodation term but could be dropped in favour of changing the meaning of 'live' to use 'receivingServiceDate' as above
            // we use it for accommodation services, to show on the 'projects' menu those moved in (not just accepted)
            // so we could drop this term, or perhaps change the meaning to only look at ReferralProject - since that could be the definitive list of accomm movements
            // we indicate 'movein' to represent past/present/future tense, so that its possible to run off future capacity expectations
            case MoveIn:
                statusCriteria = moveIn(qr, false);
                receivedOnlyDuringPeriodCriteria = getReceivedDuringPeriodCriteria(qr);
                break;

            case MoveInAtEnd:
                statusCriteria = moveIn(qr, true);
                break;

            // waiting - used as an accommodation criteria at the moment since it uses receivingServiceDate
            // shows those accepted but had some waiting during the period [=movedin + those not movedin at the 'to' date]
            // non-accommodation services could have the receivingService set using autoStart - but that will just save the same date
            // therefore, non-accommodation services 'waiting' list will behave like live - however, some non-accomm services operate a waiting list 'referral aspect'
            // the 'waiting list' referral aspect simply saves a waitingListScore property on the referral but it also has a separate 'start' (non-acccom)
            // and therefore filtering on the services which are relevant to the waiting status would work
            // and we should not restrict this to accommodation services only
            case Waiting:
                statusCriteria = waiting(qr, false);
                receivedOnlyDuringPeriodCriteria = getReceivedDuringPeriodCriteria(qr);
                break;

            // all those waiting at the end of the period
            // which is different to 'waiting' which is anything that was waiting at some point during the period
            case WaitingAtEnd:
                statusCriteria = waiting(qr, true);
                break;

            // not rejected, not closed - so at some point were live/waiting/incomplete during the period
            case Ongoing:
                statusCriteria = ongoing(qr);
                receivedOnlyDuringPeriodCriteria = getReceivedDuringPeriodCriteria(qr);
                break;

            // rejected
            // see ReferralListStatusTest
            case Signposted:
                statusCriteria = rejected(ReferralStatusName.Signposted, qr);
                break;

            case SignpostedReferral:
                statusCriteria = rejected(ReferralStatusName.SignpostedReferral, qr);
                break;

            case SignpostedService:
                statusCriteria = rejected(ReferralStatusName.SignpostedService, qr);
                break;

            // closed acts independently to other statuses
            // the only restriction on when it is applied is through the ui - which disables the 'close off' link unless they are accepted
            case Exited:
                statusCriteria = exited(qr);
                break;

            /*case ClosedSuccessful:
                statusCriteria = exited(qr);
                break;

            case ClosedUnsuccessful:
                statusCriteria = exited(qr);
                break;*/

            default:
                throw new IllegalArgumentException("status unknown: " + status.toString());
        }
        return ExpressionUtils.allOf(statusCriteria, receivedOnlyDuringPeriodCriteria);
    }

    /**
     * @see PredicateSupport#applyLocalDateTimeRange
     */
    private DateTime fudgeUtc(DateTime localFrom) {
        return localFrom.withZone(DateTimeZone.UTC);
    }

    private BooleanExpression created(QReferralServiceRecipient serviceRecipient) {
        return serviceRecipient.created.goe(fromParam).and(serviceRecipient.created.lt(toParam));
    }

    private BooleanExpression received(QReferral qr) {
        return qr.receivedDate.goe(fudgeUtc(fromParam)).and(qr.receivedDate.lt(fudgeUtc(toParam)));
    }

    /**
     * @param asAtEnd The method caters for a date period and a 'now' date - since this is how its used by the
     * referral list status group. Specifying asAtEnd indicates we only want those waiting at the end of the period.
     */
    private Predicate incomplete(QReferral qr, boolean asAtEnd) {

        DateTime fromLocal = asAtEnd ? toParam.minusDays(1) : fromParam;

        // incomplete when the decisionMadeOn is within the period (which assumes there was some incomplete period - even if seconds!) or null
        // include if decisionMadeOn is > from or null
        BooleanExpression dateWhere1 = qr.decisionMadeOnDT.goe(fromLocal).or(qr.decisionMadeOnDT.isNull());
        // HOWEVER, because we are basing the query on '> from', that includes future referrals, which we need to limit
        // we could limit by decisionMadeOn again, but the null complicates it
        // so we include those referrals where the receivedDate < to
        // it might be possible the receivedDate is null - so we go for created - then we have 2 automated dates
        // NB the automated dates could be outside the range of the report, but the received date in it: we need to force receivedDate in that case
        // however, we use receivedDate in the 'receivedOnly' boolean check above - but this result is meant to highlight incomplete
        BooleanExpression dateWhere2 = qr.serviceRecipient.created.lt(toParam);

        // we ignore rejected - since this query is before any decision 'flow' progresses

        return ExpressionUtils.allOf(dateWhere1, dateWhere2);
    }

    private Predicate accepted(QReferral qr) {
        BooleanExpression acceptedCriteria = qr.decisionMadeOnDT.goe(fromParam).and(qr.decisionMadeOnDT.lt(toParam));
        // exclude rejected at any time because any rejected should never have been 'accepted'
        return ExpressionUtils.allOf(acceptedCriteria, neverRejected);
    }

    private Predicate live(QReferral qr, boolean asAtEnd) {

        DateTime fromLocal = asAtEnd ? toParam.minusDays(1) : fromParam;

        // ideally it should be receivingServiceDate BUT non-accommodation services don't keep this flag up to date
        // also, keep in mind that receivingServiceDate can be used as a projected date until the actual moved in 'status' flag receivingService is set
        // so we would need to add additional logic and update tables to cope for non-accommodation services:
        //         ensure those without a receivingServiceDate taskdefinition get an autoStart referral aspect
        //         ensure those without a receivingServiceDate have set the startDate=accepted datetime and receivingService=1 in the sql - unless they are rejected
        //         ensure when saving accepted - that the referral aspect 'autoStart' sets receivingServiceDate and receivingService every time
        //         if rejected, then remove the receivingServiceDate and flag - but only if non-accommodation, since we need to ensure an accurate accommodation history
        // so for now, we just treat as accepted
        // which may affect accommodation services ability to distinguish between live and those who haven't got a bed yet (since receivingService is to say 'movedin')
        // but its a subtle difference, and who is to say 'live' shouldn't include those waiting for a bed anyway?
        // also - the 'projects' menu item uses 'movedin' status anyway - see below

        // decisionMadeOn can mean rejected or accepted - but we exclude rejected later, which means this date refers to accepted
        // exclude only if decisionMadeOn (accepted) > to, so include when decisionMadeOn < to [this excludes null/no decisions]
        BooleanExpression dateWhere1 = qr.decisionMadeOnDT.lt(toParam);

        // exclude only if exited <= from, so include when exited >= from
        // or if exited is null
        BooleanExpression dateWhere2 = qr.exited.goe(fudgeUtc(fromLocal)).or(qr.exited.isNull());

        // by excluding rejected, we know the flow of dates used above are a 'successful' path
        // any rejected should not have been accepted - and therefore never live
        return ExpressionUtils.allOf(dateWhere1, dateWhere2, neverRejected);
    }

    /**
     * @param asAtEnd The method caters for a date period and a 'now' date - since this is how its used by the
     * referral list status group. Specifying asAtEnd indicates we only want those waiting at the end of the period.
     */
    private Predicate waiting(QReferral qr, boolean asAtEnd) {

        DateTime fromLocal = asAtEnd ? toParam.minusDays(1) : fromParam;

        // we are deemed waiting if receivingServiceDate is set after accepted (which it should always be), or null
        // so exclude only if receivingServiceDate < from, so include when receivingServiceDate >= from date or null
        BooleanExpression dateWhere2 = qr.receivingServiceDate.goe(fudgeUtc(fromLocal));

        // or is null
        // NB this would show lots for non-accom services without the 'autoStart' fix - it will be the same as 'live'
        dateWhere2 = dateWhere2.or(qr.receivingServiceDate.isNull());

        // HOWEVER, because we are basing the query on '> from', that includes future referrals, which we need to limit
        // ALSO we need to avoid other status, such as incomplete
        // we can do both by using the accepted date, decisionMadeOn
        // exclude only if decisionMadeOn (accepted) > to, so include when decisionMadeOn < to [this excludes null/no decisions]
        BooleanExpression dateWhere1 = qr.decisionMadeOnDT.lt(toParam);

        // exclude only if exited <= from, so include when exited >= from
        // or if exited is null
        BooleanExpression dateWhere3 = qr.exited.goe(fudgeUtc(fromLocal)).or(qr.exited.isNull());

        // by excluding rejected, we know the flow of dates used above are a 'successful' path
        // any rejected should not have been accepted - and therefore can't be waiting
        return ExpressionUtils.allOf(dateWhere2, dateWhere1, dateWhere3, neverRejected);
    }

    private Predicate moveIn(QReferral qr, boolean asAtEnd) {

        DateTime fromLocal = asAtEnd ? toParam.minusDays(1) : fromParam;

        // exclude only if receivingServiceDate > to, so include when receivingServiceDate < to [this excludes null/no receivingServiceDate]
        // we don't test the boolean status 'receivingService' flag because it serves little purpose
        // if the dates are in the past then we assume the receivingServiceDate is accurate, if its in the future its by definition estimated
        BooleanExpression dateWhere1 = qr.receivingServiceDate.lt(fudgeUtc(toParam));
        if (!asAtEnd) {
            dateWhere1 = dateWhere1.and(qr.receivingServiceDate.goe(fudgeUtc(fromParam)));
        }

        // exclude only if exited <= from, so include when exited >= from
        // or if exited is null
        BooleanExpression dateWhere2 = qr.exited.goe(fudgeUtc(fromLocal)).or(qr.exited.isNull());

        // by excluding rejected, we know the flow of dates used above are a 'successful' path
        // any rejected should not have been accepted - and therefore never moved in
        return ExpressionUtils.allOf(dateWhere1, dateWhere2, neverRejected);
    }

    private Predicate ongoing(QReferral qr) {

        // exclude those closed during the whole period
        // so include exited > from or null
        // or if exited is null
        BooleanExpression dateWhere2 = qr.exited.goe(fudgeUtc(fromParam)).or(qr.exited.isNull());

        // exclude those rejected during the whole period
        // so include rejected > from or null
        BooleanExpression dateWhere4 = qr.decisionMadeOnDT.goe(fromParam);
        // AND rejected is not null
        dateWhere4 = dateWhere4.and(qr.signpostedComment.isNotNull().or(qr.signpostReason.isNotNull()));
        // OR null
        dateWhere4 = dateWhere4.or(neverRejected);

        // HOWEVER, because we are basing the query on '> from', that includes future referrals, which we need to limit
        BooleanExpression dateWhere3 = qr.serviceRecipient.created.lt(toParam);

        return ExpressionUtils.allOf(dateWhere2, dateWhere4, dateWhere3);
    }

    private Predicate rejected(ReferralStatusName status, QReferral qr) {

        // rejected in the period - using rejected not null below the decisionMadeOn now refers to when it was rejected
        BooleanExpression dateWhere1 = qr.decisionMadeOnDT.lt(toParam);
        // rejected is not null - which we maintain also in ReferralFlowAction if the status ever switches to accepted again

        // if reject at referral
        BooleanExpression rejectedPoint = null;
        if (ReferralStatusName.SignpostedReferral.equals(status)) {
            rejectedPoint = qr.acceptedReferral.isFalse();
            // if reject at service
        } else if (ReferralStatusName.SignpostedService.equals(status)) {
            rejectedPoint = qr.acceptedReferral.isTrue();
        }

        // HOWEVER, we are not using the 'from' date which we need to
        // if we use decisionMadeOn, then that would represent those rejected in the period, but the 'as at' approach would not work
        // so we create a boolean asAt to drop this clause if necessary
        BooleanExpression dateWhere2 = null;
        if (!asAt) {
            dateWhere2 = qr.decisionMadeOnDT.goe(fromParam);
        }

        return ExpressionUtils.allOf(dateWhere1, dateWhere2, anyRejected, rejectedPoint);
    }

    private Predicate exited(QReferral qr) {

        // closed in the period - using closed not null below the decisionMadeOn now refers to when it was rejected
        BooleanExpression dateWhere1 = qr.exited.lt(fudgeUtc(toParam));
        // HOWEVER, we are not using the 'from' date which we need to
        // if we use exited, then that would represent those rejected in the period, but the 'as at' approach would not work
        // so we create a boolean asAt to drop this clause if necessary
        BooleanExpression dateWhere2 = null;
        if (!asAt) {
            dateWhere2 = qr.exited.goe(fudgeUtc(fromParam));
        }

        return ExpressionUtils.allOf(dateWhere1, dateWhere2, neverRejected);
    }

}
