package com.ecco.dao;

import com.ecco.dom.agreements.ClientSalesInvoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

/**
 * @since 12/10/2016
 */
public interface ClientSalesInvoiceRepository extends JpaRepository<ClientSalesInvoice, Integer>, QuerydslPredicateExecutor<ClientSalesInvoice> {

    Stream<ClientSalesInvoice> streamByServiceRecipientId(Integer serviceRecipientId);

    ClientSalesInvoice findByServiceRecipientIdAndInvoiceDate(Integer serviceRecipientId, LocalDate invoiceDate);

    @Query("select l.workUuid from ClientSalesInvoice i left join i.lines l where l.workUuid in (:workUuids)")
    List<UUID> findMatchingInvoiceLineWorkUuid(@Param("workUuids") List<UUID> workUuids);
}
