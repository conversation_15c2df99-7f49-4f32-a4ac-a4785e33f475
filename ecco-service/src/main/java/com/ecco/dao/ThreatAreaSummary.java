package com.ecco.dao;

import java.util.UUID;

public class ThreatAreaSummary {

    private final UUID id;
    private final String riskAreaName;
    private final Long riskAreaId;
    private final UUID workId;
    private final String levelMeasure;
    private final int level;
    private final String trigger;
    private final String control;

    ThreatAreaSummary() {
        this(null, null, null, null, null, -99, null, null); // for cglib
    }

    public ThreatAreaSummary(UUID id, String riskAreaName, Long riskAreaId,
            UUID workId, String levelMeasure, int level, String trigger, String control) {
        this.id = id;
        this.riskAreaName = riskAreaName;
        this.riskAreaId = riskAreaId;
        this.workId = workId;
        this.levelMeasure = levelMeasure;
        this.level = level;
        this.trigger = trigger;
        this.control = control;
    }

    public UUID getId() {
        return id;
    }

    public String getRiskAreaName() {
        return riskAreaName;
    }

    public Long getRiskAreaId() {
        return riskAreaId;
    }

    public UUID getWorkId() {
        return workId;
    }

    public String getLevelMeasure() {
        return levelMeasure;
    }

    public int getLevel() {
        return level;
    }

    public String getTrigger() {
        return trigger;
    }

    public String getControl() {
        return control;
    }
}
