package com.ecco.dao;

import com.ecco.dom.EvidenceFormWork;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

public interface EvidenceFormWorkRepository extends EvidenceFormWorkRepositoryCustom,
        QueryDslPredicateAndProjectionExecutor<EvidenceFormWork, UUID>,
        CrudRepositoryWithFindOne<EvidenceFormWork, UUID> {

    @Modifying
    @Query("UPDATE EvidenceFormWork w " +
            "SET w.version = w.version + 1, w.signature.id = :signatureId " +
            "WHERE w.id IN :workIds")
    void attachSignature(@Param("workIds") List<UUID> workIds, @Param("signatureId") UUID signatureId);

}
