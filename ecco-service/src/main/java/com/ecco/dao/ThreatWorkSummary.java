package com.ecco.dao;

import java.util.List;
import java.util.UUID;

import com.ecco.dom.BaseOutcomeBasedWorkSummary;
import com.ecco.dom.ContactImpl;
import com.querydsl.core.annotations.QueryProjection;

import org.joda.time.DateTime;
import org.joda.time.Instant;

/** Represents just a summary of threat work without pulling in all the eager relationships.
 * Used by ThreatWorkController for the web-api. */
public class ThreatWorkSummary extends BaseOutcomeBasedWorkSummary<EvidenceThreatActionSummary> {

    private List<EvidenceFlagSummary> flags;
    private List<ThreatAreaSummary> riskAreas;
    private List<UUID> handledSupportWorkIds;

    // for cglib
    ThreatWorkSummary() {
        super(null, null, null, null, null, null, null, null, null, null, null, null);
    }

    @QueryProjection
    public ThreatWorkSummary(UUID id, Instant requestedDelete, Long taskDefId, Integer serviceRecipientId, Integer serviceAllocationId,
                             ContactImpl author, String comment,
                             Integer commentTypeId, Integer commentMinutesSpent, UUID signatureId,
                             DateTime workDate, DateTime createdDate) {
        super(id, requestedDelete, taskDefId, serviceRecipientId, serviceAllocationId, author, comment, commentTypeId, commentMinutesSpent, signatureId,
                workDate, createdDate);
    }

    public List<EvidenceFlagSummary> getFlags() {
        return this.flags;
    }

    public void setFlags(List<EvidenceFlagSummary> flags) {
        this.flags = flags;
    }

    public List<ThreatAreaSummary> getRiskAreas() {
        return this.riskAreas;
    }

    public void setRiskAreas(List<ThreatAreaSummary> riskAreas) {
        this.riskAreas = riskAreas;
    }

    public List<UUID> getHandledSupportWorkIds() {
        return handledSupportWorkIds;
    }

    public void setHandledSupportWorkIds(List<UUID> handledSupportWorkIds) {
        this.handledSupportWorkIds = handledSupportWorkIds;
    }

}
