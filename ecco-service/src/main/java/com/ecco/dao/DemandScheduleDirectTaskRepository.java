package com.ecco.dao;

import com.ecco.dom.agreements.DemandScheduleDirectTask;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import com.ecco.infrastructure.spring.data.QueryDslPredicateAndProjectionExecutor;

import java.util.List;
import java.util.UUID;

public interface DemandScheduleDirectTaskRepository extends CrudRepositoryWithFindOne<DemandScheduleDirectTask, Integer>,
        QueryDslPredicateAndProjectionExecutor<DemandScheduleDirectTask, Integer> {

    List<DemandScheduleDirectTask> findAllByScheduleId(long scheduleId);

    void deleteByTaskInstanceId(UUID taskInstanceId);
}
