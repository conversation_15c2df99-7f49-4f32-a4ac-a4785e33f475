package com.ecco.dto;

import java.io.Serializable;

import lombok.Data;
import org.joda.time.LocalDate;

@Data
public class ReferralRelationshipDto implements Serializable {

    // TODO apply properties from ReferralFlowAction.setupReferralHeader
    // then add status graph setter/getter etc, so can have an array of these for multiple referrals

    Long primaryReferralId;
    Long referralId;
    Long clientId;
    String clientDisplayName;
    LocalDate birthDate; // sneakily add a client info here, but could/should return the view model
    String relationship;

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((clientDisplayName == null) ? 0 : clientDisplayName.hashCode());
        result = prime * result + ((relationship == null) ? 0 : relationship.hashCode());
        return result;
    }
    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ReferralRelationshipDto other = (ReferralRelationshipDto) obj;
        if (clientDisplayName == null) {
            if (other.clientDisplayName != null)
                return false;
        } else if (!clientDisplayName.equals(other.clientDisplayName))
            return false;
        if (!relationship.equals(other.relationship))
            return false;
        return true;
    }

}
