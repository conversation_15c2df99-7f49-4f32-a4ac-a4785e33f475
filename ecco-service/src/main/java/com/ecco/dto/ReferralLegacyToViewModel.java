package com.ecco.dto;

import com.ecco.config.dom.SoftwareModule;
import com.ecco.config.service.SoftwareModuleService;
import com.ecco.dom.Referral;
import com.ecco.dom.contacts.AddressLike;
import com.ecco.service.ReferralService;
import com.ecco.serviceConfig.dom.ServiceType;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.viewModel.TaskDefinitionEntryViewModel;
import com.ecco.workflow.WorkflowService;
import com.ecco.workflow.WorkflowTask;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ui.ModelMap;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * A file to populate to put in the modelMap for jsp.
 * This allows us to clearly see what is still required server-side.
 * TODO Please also see/merge with referralView_setUpVars.jspf from globalCustomOverviewDef.
 */
public class ReferralLegacyToViewModel implements Serializable {

    final private ReferralService referralService;
    final private WorkflowService workflowService;
    final private ServiceTypeService serviceTypeService;
    final private SoftwareModuleService softwareModuleService;

    public ReferralLegacyToViewModel(ReferralService referralService,
                                     ServiceTypeService serviceTypeService,
                                     WorkflowService workflowService,
                                     SoftwareModuleService softwareModuleService) {
        this.referralService = referralService;
        this.serviceTypeService = serviceTypeService;
        this.workflowService = workflowService;
        this.softwareModuleService = softwareModuleService;
    }

    public ModelMap apply(long referralId) {
        ModelMap model = new ModelMap();
        Referral r = referralService.getReferral(referralId);

        model.put("quickGuideId", "quickGuide");
        model.put("serviceRecipientId", r.getServiceRecipientId());

        // for overview tab - we ignore for now
        model.put("hasOverview", true);
        model.put("referral", r);

        // for printable details
        model.put("referralId", r.getId());
        model.put("referralIdCode", r.getIdCode());
        //model.put("referralServiceName", r.getReferredService().getName());
        model.put("keyWorkerDisplayName", r.getSupportWorker() != null ? r.getSupportWorker().getDisplayName() : "");
        if (r.getClient() != null) {
            // for appointments tab
            model.put("calendarId", r.getClient().getContact().getCalendarId());

            model.put("clientId", r.getClient().getId());
            model.put("clientIdCode", r.getClient().getIdCode()); // this triggers the client details on the grab sheet
            model.put("displayName", r.getClient().getDisplayName());
            model.put("clientBirthDate", r.getClient().getBirthDate());
            model.put("clientAge", r.getClient().getAge());
            model.put("clientGender", r.getClient().getGender() == null ? null : r.getClient().getGender().getName());
            model.put("contactId", r.getClient().getContact().getId());
            model.put("preferredContactInfo", r.getClient().getContact().getPreferredContactInfo());
            model.put("mobileNumber", r.getClient().getContact().getMobileNumber());
            model.put("phoneNumber", r.getClient().getContact().getPhoneNumber());
            model.put("email", r.getClient().getContact().getEmail());

            String adrStr = "";
            // NB getAddressLike() was removed in "DEV-2422 Remove addressLike" which did prioritise the address over
            // addressLocation but seemingly wasn't used (commit msg).
            // As long as the address is correct on the client file against the addressLocation then its okay
            // and it seems SRAddressLocationCCHandler ensures that.
            //AddressLike a = r.getClient().getContact().getAddressLike();
            AddressLike a = r.getClient().getContact().getAddress();
            // we need to check for null since ECCO-184
            // Hibernate considers component to be NULL if all its properties are NULL (and vice versa)
            // see http://stackoverflow.com/questions/1324266/can-i-make-an-embedded-hibernate-entity-non-nullable
            if (a != null) {
                adrStr = a.toCommaSepString();
            }
            model.put("contactAddress", adrStr);
            model.put("clientKeyCode", r.getClient().getKeyCode());
            model.put("clientKeyWord", r.getClient().getEmergencyKeyword());
            // the clientName becomes a parameter for the calendar
            // javascript receives the parameters, but the + for spaces is taken as a plus
            // the encoding of spaces to + appears to be correct
            // and so it appears javascript does not decode properly
            // which is also backed up by some online research - http://stackoverflow.com/questions/2700721/does-urldecode-handle-plus-correctly
            // the javascript - http://xkr.us/articles/javascript/encode-compare/
            // in fact the solution is to use jsp to pick up the param
            //String clientNameJs = StringUtils.replace(clientName, " ", "%20");
            //scope.put("clientNameJs", clientNameJs);
        }

        //  for referralViewStatusBarNew - hact
        List<SoftwareModule> enabledSoftwareModules;
        model.put("enabledSoftwareModules", softwareModuleService.getEnabledModules());

        // for referralViewStatusBarNew - hact
        var svc = r.legacyLoadService();
        model.put("enableHact", StringUtils.isBlank(svc.getParameterAsString("hact.exclude"))
                || StringUtils.equalsIgnoreCase("false", svc.getParameterAsString("hact.exclude")));

        // match existing logic - which is to check the referral then check the config (dto at least)
        Long primaryReferralId = referralService.getPrimaryReferralId(r);
        boolean allowed = primaryReferralId != null;
        model.put("multipleReferralsAllowed", allowed);
        if (allowed) {
            model.put("primaryReferralId", primaryReferralId);
        }

        // DUPL
        // for referralView_calcEvidenceLabels & contact-appointments-control
        //model.put("referralId", r.getId());

        // DUPL
        // for referral-all-referrals-control
        //model.put("clientId", r.getClient().getId());

        // DUPL
        // for menus/referralView calendar
        //model.put("contactId", r.getClient().getContact().getId());

        model.put("avatarId", r.getClient().getContact().getAvatarId());

        // for referral-child-referrals-control
        model.put("serviceId", svc.getId());

        // for showChildServicesTab: not empty referral.referredService.parameters['allocateToServices']
        model.put("allocateToServices", StringUtils.isNotBlank(svc.getParameterAsString("allocateToServices")));

        // 'evidenceGraph' not populated

        // NB UNCACHED (as it was before), since we don't have a cached version as a service
        // NB we should migrate to findOneDto
        ServiceType st = serviceTypeService.findOne(Long.valueOf(r.loadConfigServiceTypeId()));
        model.put("serviceType", st);
        model.put("currentTaskDefinition", r.getCurrentTaskDefinition());
        // for referralView_calcEvidenceLabels
        st.getTaskDefinitions().size();
        model.put("taskDefinitions", st.getTaskDefinitions());
        // 'workflowTasksByName' from referralFlow.xml, as below
        // referralTasksFilterKey used to restrict 'tasks' list
        // from referralFlow.xml url; ServiceRecipientEventDecorator (links to file); supportPlanFlow (opens tasksToShowClientView)
        // options are tasksToShowRestricted (default) or tasksToShowClientView
        // tasksToShow set as REFERRAL_VIEW.tasksToShowRestricted or REFERRAL_VIEW.tasksToShowClientView
        // 'tasksToShow' is result of above (see ReferralFlowAction.setupReferralHeader)
        if (st.getWorkflow().isPresent() && workflowService.activitiWorkflowEnabledFor(st.getWorkflowProcessKey(), r.getServiceRecipientId().toString())) {
            String referralTasksFilterKey = "tasksToShowRestricted"; // ignore tasksToShowClientView
            String tasksToShow = st.getTaskDefinitionById(TaskDefinitionEntryViewModel.REFERRAL_VIEW)
                    .map( ra -> ra.getSetting(referralTasksFilterKey) )
                    .orElse(null);
            List<WorkflowTask> workflowTasks = workflowService.getFilteredWorkflowTasks(st.getWorkflowProcessKey(),
                    r.getServiceRecipientId().toString(), referralTasksFilterKey, tasksToShow);
            model.put("workflowTasks", workflowTasks);
            Map<String, WorkflowTask> workflowTasksByName = workflowService.getWorkflowTasksAsMap(workflowTasks);
            model.put("workflowTasksByName", workflowTasksByName);
        }
        else {
            // TODO: Do we need linear workflow? or even activiti stuff above
        }
        return model;
    }

}
