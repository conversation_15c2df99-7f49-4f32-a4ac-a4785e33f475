import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    // Support convention plugins written in Kotlin. Convention plugins are build scripts in 'src/main' that automatically become available as plugins in the main build.
    `kotlin-dsl`
}

val kotlinVersion = "1.9.20"

/** This is how we specify the plugin versions we're using because we can't specify the version in the convention file */
dependencies {
    implementation("org.owasp:dependency-check-gradle:10.0.2")
    implementation("com.gorylenko.gradle-git-properties:gradle-git-properties:2.4.2")
    implementation("org.jetbrains.kotlin.plugin.jpa:org.jetbrains.kotlin.plugin.jpa.gradle.plugin:$kotlinVersion")
    implementation("org.jetbrains.kotlin.jvm:org.jetbrains.kotlin.jvm.gradle.plugin:$kotlinVersion")
    implementation("org.jetbrains.kotlin.kapt:org.jetbrains.kotlin.kapt.gradle.plugin:$kotlinVersion")
}

repositories {
    // Use the plugin portal to apply community plugins in convention plugins.
    gradlePluginPortal()
}

tasks {
    withType<KotlinCompile> {
        compilerOptions.jvmTarget.set(JvmTarget.JVM_17)
    }
//compileTestKotlin {
//    kotlinOptions {
//        jvmTarget = JavaVersion.VERSION_11
//    }

    register<WriteProperties>("writeDependencyProperties") {
        destinationFile.set(layout.buildDirectory.file("resources/main/dependencies.properties"))

        property("kotlin.version", kotlinVersion)
    }

    named("processResources") {
        dependsOn("writeDependencyProperties")
    }
}
