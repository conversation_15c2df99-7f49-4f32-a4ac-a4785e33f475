import $ = require("jquery");

import BaseControl = require("../controls/BaseControl");
import ViewSignatureControl = require("../controls/ViewSignatureControl");
import * as commandDtos from "ecco-dto/command-dto";
import {SendEmailCommand, SendEmailCommandDto} from "ecco-commands";
import {
    AreaUpdateCommandDto,
    BaseOutcomeBasedWork,
    BaseServiceRecipientCommandDto,
    CommandDtoServer,
    DeleteEvidenceCommandDto,
    DeleteEvidenceRequestCommandDto,
    FormEvidenceCommandDto,
    GoalUpdateCommandDto,
    QuestionAnswerCommandDto,
    SignWorkCommandDto,
    WorkEvidenceCommandDto
} from "ecco-dto/evidence-dto";
import {
    AddedRemoved,
    adminModeEnabled,
    CalendarDays,
    Change,
    DtoFieldValue,
    EvidenceGroup, iso8601UtcToFormatLocalShort, iso8601UtcToFormatLocalShortDate,
    NumberChangeOptional,
    redAmberGreens,
    ReferralDto,
    SessionData,
    SmartStepStatus,
    statusMessages,
    StringChangeOptional, UpdateCommandDto
} from "ecco-dto";
import {EccoDate, EccoDateTime, EccoDateTime as DateTime, StringUtils} from "@eccosolutions/ecco-common";
import {
    AddressHistoryCommand,
    AddressHistoryCommandDto,
    AppointmentActionCommand,
    AppointmentActionCommandDto,
    AppointmentRecurringActionCommand,
    AppointmentRecurringActionCommandDto,
    AreaUpdateCommand,
    BaseServiceRecipientTaskUpdateCommand,
    CommentCommand,
    CreateReferralCommand,
    CreateReferralCommandDto,
    DeleteEvidenceCommand,
    DeleteEvidenceRequestCommand,
    DeleteRequestServiceRecipientCommand,
    DeleteServiceRecipientCommand,
    FormEvidenceUpdateCommand,
    GoalUpdateCommand,
    isAddedRemoved,
    QuestionAnswerCommand,
    ServiceRecipientAttributeChangeCommand,
    SignWorkCommand,
    UserAccessAuditCommand,
    UserChangeCommand,
    UserChangeDto,
    UserMfaResetCommand,
    UserMfaResetCommandDto
} from "ecco-commands";
import {
    asAgreedRejected,
    asYesNo,
    setTextAsyncArray,
    setTextAsyncValue,
    summariseAgency,
    summariseAppointmentType,
    summariseCommentType,
    summariseFundingSource,
    summariseIndividual,
    summariseListDef,
    summariseProject,
    summariseResidence,
    summariseService, summariseServiceType, summariseTaskHandle,
    toWords
} from "./dataRenderers";
import {
    CalendarEntryDto,
    ServiceRecipientCalendarEntryCommand,
    ServiceRecipientCalendarEntryDto
} from "ecco-commands";
import {
    DeleteRequestServiceRecipientCommandDto,
    DeleteServiceRecipientCommandDto,
    ReferralTaskEditSignedAgreementCommandDto,
    ServiceRecipientTaskBaseCommandDto,
    UserAccessAuditCommandDto
} from "ecco-dto/evidence/evidence-command-dto";
import {baseURI} from '../environment';
import {getGlobalEccoAPI, translateTaskName} from "ecco-components";
import {FinanceReceiptCommand} from "ecco-finance";
import {CommandViewHandler} from "ecco-components";
import {GroupActivityCommand, GroupActivityCommandDto} from "../groupsupport/commands";

const MAX_TITLE_LEN = 50;


// TODO this is very slow - according to a quick look at chrome performance (.append and setTextAsyncValue)
/** If we pass in a promise it will be set when the promise resolves */
function appendChange($el: $.JQuery, field: string,
                      from: string | number | Promise<string | null> | null | undefined,
                      to: string | number | Promise<string | null> | null | undefined,
                      ignoreFrom = false) {
    let $dd = $("<dd>").addClass("updated"); // .updated is for myplan history
    if (field.endsWith(" id")) {
        field = field.substr(0, field.length - 3);
    }
    else if (field.endsWith(" change")) {
        field = field.substr(0, field.length - 7);
    }
    $el.append( $("<dt>").text(field) )
        .append($dd);
    // NB catch falsy - would need to check false if boolean also allowed
    if (from || from === 0) {
        const $del = $("<del>");
        $dd.append($del);

        setTextAsyncValue($del, from);
        if (to || to === 0) {
            const $ins = $("<ins>");
            $dd.append("<br>")
               .append($ins);
            setTextAsyncValue($ins, to);
        }
    }
    else if (to || to === 0) {
        setTextAsyncValue($dd, to);
    }
    else {
        if (!ignoreFrom) {
            // this can happen if 'comment' is from: null to: "" on old commands until commit 1a2d7318
            console.info(`appendChange should not have both from/to params as null/undefined for field '${field}'`);
        } else {
            // this is not an appendChange as much as showing a value directly which may not even have a value, so ignore
        }
    }
}

/** If we pass in a promise it will be set when the promise resolves */
function appendAddedRemoved($el: $.JQuery, field: string,
                            from: string[] | number[] | Promise<string>[],
                            added: string[] | number[] | Promise<string>[],
                            removed: string[] | number[] | Promise<string>[]) {
    let $dd = $("<dd>").addClass("updated"); // .updated is for myplan history
    if (field.endsWith(" id")) {
        field = field.substr(0, field.length - 3);
    }
    if (field.endsWith(" ids")) {
        field = field.substr(0, field.length - 4);
    }
    else if (field.endsWith(" change")) {
        field = field.substr(0, field.length - 7);
    }
    $el.append( $("<dt>").text(field) )
        .append($dd);
    if (from && from.length) {
        const $from = $("<span>");
        $dd.append("was: ").append($from);
        setTextAsyncArray($from, from);

        if (removed.length) {
            const $del = $("<del>");
            $dd.append("<br>removed: ")
                .append($del);
            setTextAsyncArray($del, removed);
        }
        if (added.length) {
            const $ins = $("<ins>");
            $dd.append("<br>added: ")
                .append($ins);
            setTextAsyncArray($ins, added);
        }
    }
    else {
        setTextAsyncArray($dd, added);
    }
}

// currently only used on emergency details lookup - see stringChangeRendererWithMessages
// also see evidenceTaskNameLookup
function messageLookup(key: string): string {
    const messages = getGlobalEccoAPI().sessionData.getMessages()
    return messages[key] || "[" + key + "]";
}

function isChange<T extends string | object | number | boolean>(value: Change<T> | T | null): value is Change<T> {
    const change = value as Change<T>;
    return change != null && (change.to != null || change.from != null);
}

/** For use with currying formattingRenderer(formatter)
 * see https://gist.github.com/donnut/fd56232da58d25ceecf1 */
function formattingRenderer<T extends string | object | number | boolean | null>(
    formatter: (T) => (string | null | Promise<string | null>), messagePrefix?: string
): ($list: $.JQuery, field: string, change: AddedRemoved<T> | Change<T> | T) => void {
    return ($list: $.JQuery, field: string, change: AddedRemoved<T> | Change<T> | T) => {
        if (isAddedRemoved(change)) {
            appendAddedRemoved($list, messagePrefix ? messageLookup(field) : toWords(field),
                               (change.from??[]).map(formatter) as string[] | Promise<string>[],
                               (change.added??[]).map(formatter)  as string[] | Promise<string>[],
                               (change.removed??[]).map(formatter) as string[] | Promise<string>[]);
        }
        else if (isChange(change)) {
            appendChange($list, messagePrefix ? messageLookup(field) : toWords(field), formatter(change.from), formatter(change.to));
        }
        else {
            appendChange($list, toWords(field), undefined, formatter(change), true);
        }
    }
}

const booleanChangeRenderer = formattingRenderer<boolean>(asYesNo);
const agreedChangeRenderer = formattingRenderer<boolean>(asAgreedRejected);
const dateChangeRenderer = formattingRenderer<string>(EccoDate.iso8601ToFormatShort);
const dateOptionalTimeChangeRenderer = formattingRenderer<string>(
    dt => dt == null ? null
        : dt.length == 10 ? EccoDate.iso8601ToFormatShort(dt)
            : EccoDateTime.iso8601IgnoreTzToDateNoMidnight(dt)
);
const dateTimeUtcChangeRenderer = formattingRenderer<string>(DateTime.iso8601UtcToFormatShort);
const dateTimeUtcToLocalChangeRenderer = formattingRenderer<string>(iso8601UtcToFormatLocalShort);
const dateTimeUtcToLocalDateChangeRenderer = formattingRenderer<string>(iso8601UtcToFormatLocalShortDate);
const dateTimeOmitMidnightChangeRenderer = formattingRenderer<string>(DateTime.iso8601IgnoreTzToDateNoMidnight);
const dayChangeRenderer = formattingRenderer<number>(n => CalendarDays[n]);
const numberChangeRenderer = formattingRenderer<number>(n => n);
const stringChangeRenderer = formattingRenderer<string>(s => s);
const stringChangeRendererWithMessages = (messagePrefix: string) => formattingRenderer<string>(s => s, messagePrefix);

const agencyRenderer = formattingRenderer<number>(summariseAgency);
const appointmentTypeChangeRenderer = formattingRenderer<number>(summariseAppointmentType);
// this is now for legacy audits, newer cmds moved to list defs
const commentTypeChangeRenderer = formattingRenderer<number>(summariseCommentType);
const fundingSourceRenderer = formattingRenderer<number>(summariseFundingSource);
const individualRenderer = formattingRenderer<number>(summariseIndividual);
const listDefChangeRenderer = formattingRenderer<number>(summariseListDef);
const locationChangeRenderer = formattingRenderer<number>(addressId => addressId); // TODO:
const pendingStatusRenderer = formattingRenderer<number>(summariseListDef);
const projectChangeRenderer = formattingRenderer<number>(summariseProject);
const residenceChangeRenderer = formattingRenderer<number>(summariseResidence);
const serviceChangeRenderer = formattingRenderer<number>(summariseService);
const serviceTypeChangeRenderer = formattingRenderer<number>(summariseServiceType);
const flagChangeRenderer = formattingRenderer<number>(summariseListDef);

//noinspection JSUnusedLocalSymbols
function choicesMapChangeRenderer($list: $.JQuery, field: string, change: {[key:string]: NumberChangeOptional}) {
    for(let key in change) {
        appendChange($list, toWords(key), summariseListDef(change[key].from), summariseListDef(change[key].to));
    }
}

//noinspection JSUnusedLocalSymbols
function stringMapChangeRenderer($list: $.JQuery, field: string, change: {[key:string]: StringChangeOptional}) {
    for(let key in change) {
        appendChange($list, toWords(key), change[key].from, change[key].to);
    }
}

// also see ViewSignatureControl
//noinspection JSUnusedLocalSymbols
const svgSignatureRenderer = function ($list: $.JQuery, signedDate: string, svg: string) {
    $list
        .append($("<dt>").addClass("updated").text("signed"))
        .append($("<dd>")
            .append(" on " + DateTime.iso8601ToFormatShort(signedDate))
            .append(ViewSignatureControl.linkForDtoInModal({
                svgXml: svg,
                id: null!,
                signedDate: signedDate,
                signedFor: null!
            })));
};

//noinspection JSUnusedLocalSymbols
function signatureRenderer($list: $.JQuery, field: string, svg: string, commandDto: ReferralTaskEditSignedAgreementCommandDto) {
    svgSignatureRenderer($list, commandDto.signedDateChange!.to!, svg);
}

//noinspection JSUnusedLocalSymbols
function signWorkSigRenderer($list: $.JQuery, field: string, svg: string, commandDto: SignWorkCommandDto) {
    svgSignatureRenderer($list, commandDto.signedDate, svg);
}

const fieldRenderers: Record<string, ($list: $.JQuery, field: string, value: DtoFieldValue,
                                    command?: commandDtos.CommandDto | BaseServiceRecipientCommandDto | ServiceRecipientTaskBaseCommandDto
                                        | AreaUpdateCommandDto | SignWorkCommandDto | CalendarEntryDto | SendEmailCommandDto
                                    ) => void> = {

    externalSource: booleanChangeRenderer,

    // work
    workDate: dateTimeOmitMidnightChangeRenderer, // this.addTimestampChangeItem($header, "work date", this.commandDto.workDate);
    attendanceStatus: formattingRenderer<number>(n => n === 0 ? "started visit" : "finished visit"),
    comment: stringChangeRenderer, // this.addChangeItem($header, "comment", this.commandDto.comment);
    commentTypeId: commentTypeChangeRenderer,
    eventStatusRateId: listDefChangeRenderer,
    // eventId - rota date
    location: formattingRenderer<string|number>(sn => typeof sn === "string" ? sn : summariseResidence(sn)),
    locationId: listDefChangeRenderer,
    clientStatusId: listDefChangeRenderer,
    clientStatusOkay: booleanChangeRenderer,
    meetingStatusId: listDefChangeRenderer,
    minsSpent: numberChangeRenderer,
    minsTravel: numberChangeRenderer,
    mileageTo: numberChangeRenderer,
    mileageDuring: numberChangeRenderer,
    riskManagementRequired: booleanChangeRenderer,
    // riskManagementHandled - array of uuid string's
    // attachmentIdsToAdd: number[];
    flagIds: flagChangeRenderer,

    // task instance
    dueDate: dateTimeOmitMidnightChangeRenderer,
    // instant, but no Z, so datetimeutc.. fails - eg "2024-10-24T19:41:42.758"
    // changed in 67783f0e Dec 2023, seemingly due to ecco-common change in d6a913ce Feb 2022 to 1.8.4 (from 1.8.2 previously)
    // but it appears there was no functional change between those versions anyway
    // so it could be an issue on a different branch - "2024-10-24T19:41:42.758" currently works with dateTimeUtcToLocalChangeRenderer
    completed: dateTimeUtcToLocalChangeRenderer,
    completedStatusId: listDefChangeRenderer,
    assignee: stringChangeRenderer,
    taskHandle: ($list: $.JQuery, field: string, value: DtoFieldValue,
                  command?: commandDtos.CommandDto | BaseServiceRecipientCommandDto | ServiceRecipientTaskBaseCommandDto
                          | AreaUpdateCommandDto | SignWorkCommandDto | CalendarEntryDto | SendEmailCommandDto) => {
        const cmd = command as BaseServiceRecipientCommandDto;
        // we can't use commandName == EditTaskCommand.TASK_NAME as the commandName persisted is taskUpdate, which could be changed
        if (cmd.taskName == "taskInstance") {
            const taskHandle = cmd.taskHandle
            const svcAllocId = cmd.latestServiceAllocationId;
            return appendChange($list, "task", undefined, summariseTaskHandle(taskHandle, svcAllocId));
        }
    },

    // SignWorkCommandDto
    svgXml: signWorkSigRenderer,

    // AddressHistoryCommandDto
    //operation: string;
    validFrom: dateTimeUtcToLocalChangeRenderer,
    //contactId?: number;
    addressLocation: numberChangeRenderer,
    buildingLocation: numberChangeRenderer,

    // AreaUpdateCommandDto
    //levelChange: levelChangeRenderer, // now a specialised rendered
    triggerChange: stringChangeRenderer,
    controlChange: stringChangeRenderer,

    // ReferralTaskEditSourceCommandDto
    agencyChange: numberChangeRenderer,
    referrerChange: numberChangeRenderer,
    selfReferralChange: booleanChangeRenderer,

    // ReferralTaskEditSignedAgreementCommandDto - data protection
    signatureSvgXml: signatureRenderer,
    reset: formattingRenderer<boolean>(asYesNo),
    userComment: formattingRenderer<string>(s => s),
    agreementDateChange: dateTimeUtcToLocalDateChangeRenderer,
    agreementStatusChange: agreedChangeRenderer,

    // DaysAttendingUpdateCommandDto
    daysAttendingChange: dayChangeRenderer,

    // ReferralTaskReferralDetailsCommandDto
    // and IncidentDetailCommandDto
    // and RepairDetailCommandDto
    // and ManagedVoidDetailCommandDto
    receivedDateChange: dateChangeRenderer,
    // srcGeographicAreaId
    categoryId: listDefChangeRenderer,
    reportedById: numberChangeRenderer,
    reportedBy: stringChangeRenderer,
    hospitalisationInvolved: booleanChangeRenderer,
    emergencyServicesInvolvedChange: booleanChangeRenderer,

    // ReferralTaskPendingStatusCommandDto
    pendingStatus: pendingStatusRenderer,

    // ReferralUpdateCommandDto
    acceptedDate: dateOptionalTimeChangeRenderer,
    signpostedBack: booleanChangeRenderer,
    signpostedReason: listDefChangeRenderer,
    signpostedAgency: agencyRenderer,
    signpostedComment: stringChangeRenderer,
    acceptedState: stringChangeRenderer, // TODO: Review how this works in practice

    // ReferralTaskEditEmergencyDetailsCommandDto extends ReferralUpdateCommandDto {
    descriptionDetails: stringChangeRendererWithMessages("form.emergencyDetails."),
    communicationNeeds: stringChangeRendererWithMessages("form.emergencyDetails."),
    emergencyKeyword: stringChangeRendererWithMessages("form.emergencyDetails."),
    emergencyDetails: stringChangeRendererWithMessages("form.emergencyDetails."),
    medicationDetails: stringChangeRendererWithMessages("form.emergencyDetails."),
    doctorDetails: stringChangeRendererWithMessages("form.emergencyDetails."),
    dentistDetails: stringChangeRendererWithMessages("form.emergencyDetails."),
    risksAndConcerns: stringChangeRendererWithMessages("form.emergencyDetails."),

    // ReferralTaskAssessmentDateCommandDto
    interviewer1: individualRenderer,
    interviewer2: individualRenderer,
    decisionDate: dateTimeUtcChangeRenderer,
    firstOfferedInterviewDate: dateTimeUtcChangeRenderer,
    // TODO also is needed location: locationChangeRenderer, - this could be done by detecting if it's already a string in the formatter

    interviewSetupComments: stringChangeRenderer,
    interviewDna: numberChangeRenderer,
    interviewDnaComments: stringChangeRenderer,

    // ReferralTaskDeliveredByCommandDto
    deliveredBy: agencyRenderer,
    deliveredByStartDate: dateTimeUtcChangeRenderer,

    // ReferralTaskFundingCommandDto
    fundingSource: fundingSourceRenderer,
    fundingPaymentRef: stringChangeRenderer,
    fundingAmount: numberChangeRenderer,
    fundingReviewDate: dateChangeRenderer,
    fundingDecisionDate: dateChangeRenderer,
    hoursOfSupport: numberChangeRenderer,
    fundingAccepted: booleanChangeRenderer,

    // ReferralTaskStartOnServiceCommandDto
    allocatedWorkerContactId: individualRenderer,
    hasStarted: booleanChangeRenderer,
    receivingServiceDate: dateChangeRenderer,

    // ClientResidenceUpdateCommandDto
    startDate: dateChangeRenderer,
    endDate: dateChangeRenderer,
    residence: residenceChangeRenderer,

    start: dateChangeRenderer,
    end: dateChangeRenderer,
    time: stringChangeRenderer, // Format is simply "hh:mm", so render as string

    // DemandSchedule
    applicableFromDate: dateChangeRenderer,
    appointmentTypeId: appointmentTypeChangeRenderer,
    durationMins: numberChangeRenderer,
    additionalStaff: numberChangeRenderer,

    tasks: stringChangeRenderer,
    days: dayChangeRenderer,


    // attributeChange commandName
    valueChange: stringChangeRenderer,
    attributePath: formattingRenderer<boolean>(str => {
        try {
            let parts = /(\w+)\.textMap\["(.+)"]/.exec(str);
            if (!parts) {
                parts = /(\w+)\.(\w+)/.exec(str)!;
            }
            const target = parts[1];
            const friendlyTarget = target == "worker" ? "staff" : target;
            return `${StringUtils.camelToSpaces(parts[2])} of ${friendlyTarget}`
        }
        catch (e) {
            return str;
        }
    }),

    // Review
    // TODO: This really is unused in .ts - we have UI to do for this one I think
    setDefaultDates: ($list: $.JQuery) => {
        $list.append($("<dt>").text("date change"))
            .append($("<dd>").text("scheduled with default dates"));
    },

    customDateChange: dateChangeRenderer,

    // ReferralTaskSPDataCommandDto
    choicesMapChanges: choicesMapChangeRenderer,
    textMapChanges: stringMapChangeRenderer,

    // ReferralTaskExitCommandDto
    exitedDateChange: dateChangeRenderer,
    exitedReasonChange: listDefChangeRenderer,
    exitedCommentChange: stringChangeRenderer,
    reviewDateChange: dateChangeRenderer,

    // ReferralTaskEditDetailsCommandDto
    // daysAttendingChange,
    // exitedDateChange,
    isPrimaryChildReferralChange: booleanChangeRenderer,
    projectChange: projectChangeRenderer,
    receivingServiceDateChange: dateChangeRenderer,

    // BuildingCommandViewModel
    resourceType: listDefChangeRenderer,

    // ClientWithContact
    mobileNumberChange: stringChangeRenderer,
    phoneNumberChange: stringChangeRenderer,
    preferredContactMethodChange: stringChangeRenderer,
    birthDateChange: dateChangeRenderer,
    genderChange: listDefChangeRenderer,
    lastNameChange: stringChangeRenderer,
    firstNameChange: stringChangeRenderer,
    knownAsChange: stringChangeRenderer,
    pronounsChange: listDefChangeRenderer,
    firstLanguageChange: listDefChangeRenderer,
    ethnicOriginChange: listDefChangeRenderer,
    religionChange: listDefChangeRenderer,
    disabilityChange: listDefChangeRenderer,
    sexualOrientationChange: listDefChangeRenderer,
    niChange: stringChangeRenderer,
    nhsChange: stringChangeRenderer,
    housingBenefitChange: stringChangeRenderer,

    // calendarEntryDto
    eventUuid: formattingRenderer<string>(s => s),
    title: stringChangeRenderer,
    //startDate: StringChangeOptional;
    allDay: booleanChangeRenderer,
    startTime: stringChangeRenderer,
    //endDate: StringChangeOptional;
    endTime: stringChangeRenderer,
    eventCategoryId: listDefChangeRenderer,
    repeatEveryDays: numberChangeRenderer,
    repeatEveryWeeks: numberChangeRenderer,

    // SendEmailCommandDto
    subject: stringChangeRenderer,
    body: stringChangeRenderer,

    // ConfigCommand
    serviceTypeId: serviceTypeChangeRenderer,
    serviceTypeIdChange: serviceTypeChangeRenderer,
    all: booleanChangeRenderer,
    // taskName: stringChangeRenderer,
    settingName: stringChangeRenderer,
    name: stringChangeRenderer,
    allowNextChange: booleanChangeRenderer,
    voteChange: stringChangeRenderer,
    cacheNames: stringChangeRenderer,
    nameChange: stringChangeRenderer,
    disabledChange: booleanChangeRenderer,

    // User
    firstName: stringChangeRenderer,
    lastName: stringChangeRenderer,
    username: stringChangeRenderer,
    email: stringChangeRenderer,
    enabled: booleanChangeRenderer,
    mfaRequired: booleanChangeRenderer,
    groups: stringChangeRenderer,
    services: serviceChangeRenderer,
    projects: projectChangeRenderer,

    // User audit
    source: stringChangeRenderer,

    // Finance receipt audit
    receiptId: formattingRenderer<number>(id => id),
    amount: numberChangeRenderer,
    receivedDate: dateChangeRenderer,
    description: stringChangeRenderer

    /*
    // GroupActivity
    discriminator_orm: formattingRenderer<string>(s => s),
    descriptionChange: stringChangeRenderer,
    serviceChange: numberChangeRenderer,
    //projectChange: numberChangeRenderer,
    activityTypeChange: numberChangeRenderer,
    venueChange: numberChangeRenderer,
    capacityChange: numberChangeRenderer,
    durationChange: numberChangeRenderer,
    startDateTimeChange: dateTimeUtcChangeRenderer,
    endDateChange: dateTimeUtcChangeRenderer
    */
};

/**
 * Acts as a group of several CommandViewHandler's which are responsible for rendering audits
 * TODO the view handlers themselves would be better without state
 */
export class CommandHistoryItemControl extends BaseControl {

    protected $header = $("<div>");
    protected $list = $("<ul>").addClass("list-unstyled changes");
    protected handlers: CommandViewHandler<any>[] = [];

    constructor(private sessionData: SessionData, private detailPermission: boolean, elm = $("<li>")) {
        super(elm);
    }

    public processHandler(handler: CommandViewHandler<any>) {
        if (this.handlers.length == 0) {
            this.processHeader(handler.getCommand());
        }
        this.handlers.push(handler);
        handler.render(this.$header[0], this.$list[0]);
    }

    private processHeader(commandDto: BaseServiceRecipientCommandDto) {
        let $firstLine = $("<div>");

        if (this.detailPermission) {
            $firstLine = $("<div>")
                    .css({"clear": "both"});
            $firstLine.append(iso8601UtcToFormatLocalShort(commandDto.timestamp));
            $firstLine
                    .append($("<span>").addClass('user')
                            .append(" recorded by ")
                            .append($("<em>").text(commandDto.userName || '(draft)')));
        }

        let $secondLine: $.JQuery | null = null;
        if (commandDto.displayName) {
            $secondLine = $("<div>")
                .css({"clear": "both"});
            $secondLine
                .append($("<span>").text(commandDto.displayName));
        }

        const $idLine = $("<div>")
            .addClass("uuid")
            .css({"clear": "both"});
        $idLine.append($("<span>")
            .css({"font-size": "0.8em", "float": "right"})
            .text('cmd id: '.concat(commandDto.uuid)));

        // don't show id's by default - but we can inspect, or do console show to see all 'uuid' class
        $idLine.hide();

        const $infoArea = $("<div>");
        $infoArea
            .append($idLine, $firstLine, $secondLine);

        this.element().append($infoArea, this.$header, this.$list);
    }

    // NB could do more like ReportCapabilityLookup - we don't need a factory: new (sessionData: SessionData, commandDto: T): CommandViewHandler<T>;
    public static createCommandViewHandler(commandDto: commandDtos.CommandDto, sessionData: SessionData): CommandViewHandler<any> {
        switch (commandDto.commandName) {
            case null:
                throw new Error("commandName must be set on the dto");

            case UserChangeCommand.discriminator:
                return new UserCoreCommandViewHandlerControl(sessionData, <UserChangeDto>commandDto);

            case UserMfaResetCommand.discriminator:
                return new UserMfaResetCommandViewHandlerControl(sessionData, <UserMfaResetCommandDto>commandDto);

            case CommentCommand.discriminator:
                return new CommentCommandViewHandlerControl(sessionData, <WorkEvidenceCommandDto>commandDto);

            case SendEmailCommand.discriminator:
                return new EmailCommandViewHandlerControl(sessionData, <SendEmailCommandDto>commandDto);

            case AreaUpdateCommand.discriminator:
                return new RiskAreaCommandViewHandlerControl(sessionData, <AreaUpdateCommandDto>commandDto);

            case GoalUpdateCommand.discriminator:
                return new GoalCommandViewHandlerControl(sessionData, <GoalUpdateCommandDto>commandDto);

            case QuestionAnswerCommand.discriminator:
                return new QuestionAnswerCommandViewHandlerControl(sessionData, <QuestionAnswerCommandDto>commandDto);

            case SignWorkCommand.discriminator:
                return new SignCommandViewHandlerControl(sessionData, <SignWorkCommandDto>commandDto);

            case DeleteEvidenceRequestCommand.discriminator:
                return new DeleteEvidenceRequestCommandViewHandlerControl(sessionData, <DeleteEvidenceRequestCommandDto>commandDto);

            case DeleteEvidenceCommand.discriminator:
                return new DeleteEvidenceCommandViewHandlerControl(sessionData, <DeleteEvidenceCommandDto>commandDto);

            case FormEvidenceUpdateCommand.discriminator:
                return new FormEvidenceCommandViewHandlerControl(sessionData, <FormEvidenceCommandDto>commandDto);

            case ServiceRecipientCalendarEntryCommand.discriminator:
                return new CalendarEntryCommandViewHandler(sessionData, <ServiceRecipientCalendarEntryDto>commandDto);

            case CreateReferralCommand.discriminator:
                return new CreateReferralCommandViewHandlerControl(sessionData, <CreateReferralCommandDto>commandDto);

            case DeleteRequestServiceRecipientCommand.discriminator:
                return new DeleteRequestServiceRecipientCommandViewHandlerControl(sessionData, <DeleteRequestServiceRecipientCommandDto>commandDto);

            case DeleteServiceRecipientCommand.discriminator:
                return new DeleteServiceRecipientCommandViewHandlerControl(sessionData, <DeleteServiceRecipientCommandDto>commandDto);

            case AddressHistoryCommand.discriminator:
                return new AddressHistoryCommandViewHandlerControl(sessionData, <AddressHistoryCommandDto>commandDto);

            case AppointmentActionCommand.discriminator:
            case AppointmentRecurringActionCommand.discriminator:
                return new AppointmentActionCommandViewHandlerControl(sessionData, <AppointmentActionCommandDto | AppointmentRecurringActionCommandDto>commandDto);

            // 'taskUpdate' discriminator represents many different taskName commands.
            // In fact, ServiceRecipientTaskBaseCommandDto also represents tasks, but their commands don't have 'taskUpdate' discriminator
            // but they get checked in the 'default' handler below, which is the same as this handler.
            case BaseServiceRecipientTaskUpdateCommand.discriminator:
                return this.determineTaskHandler(sessionData, commandDto);

            case ServiceRecipientAttributeChangeCommand.discriminator:
                return new SimpleCommandViewHandlerControl("data update", sessionData, commandDto);

            case UserAccessAuditCommand.discriminator:
                return new UserAccessAuditCommandViewHandler(sessionData, commandDto as UserAccessAuditCommandDto);

            case FinanceReceiptCommand.discriminator:
                return new SimpleCommandViewHandlerControl("receipt", sessionData, commandDto);

            case GroupActivityCommand.discriminator:
                return new GroupActivityCommandViewHandler(sessionData, commandDto as GroupActivityCommandDto);

            default:
                // force uncaught commands through a raw handler, if they aren't really a task
                return this.isTaskCommandDto(commandDto)
                    ? this.determineTaskHandler(sessionData, commandDto)
                    // throw new Error("No match found for command name:" + commandDto.commandName);
                    : new RawCommandViewHandlerControl(sessionData, commandDto);
        }
    }

    private static isTaskCommandDto(cmd: any): cmd is ServiceRecipientTaskBaseCommandDto {
        return (<ServiceRecipientTaskBaseCommandDto>cmd).taskName !== undefined;
    }

    /**
     * Convenience method to separate the task commands handlers.
     * Unfortunately, whilst some commands extend BaseReferralTaskUpdateCommand, the newer ones don't, but they can all be
     * represented by ServiceRecipientTaskBaseCommandDto - so we cast to that.
     */
    private static determineTaskHandler(sessionData: SessionData, commandDto: commandDtos.CommandDto) {
        return new ReferralCommandViewHandlerControl(sessionData, <ServiceRecipientTaskBaseCommandDto>commandDto);
    }
}

abstract class BaseCommandViewHandlerControl<T extends commandDtos.CommandDto> extends BaseControl implements CommandViewHandler<T> {

    constructor(protected sessionData: SessionData, protected commandDto: T) {
        super($("<div>"));
    }

    public getCommand() {
        return this.commandDto;
    }

    public getTitleForCommand(): string {
        const svcAllocId = this.getServiceAllocationId();
        const taskName = this.getTaskName();
        if (svcAllocId != null && taskName != null) {
            const st = this.sessionData.getServiceTypeByServiceCategorisationId(svcAllocId);
            // titleRaw then referralView. then referralBreadcrumb then camel to spaces
            return st.lookupTaskName(taskName);
        } else {
            return StringUtils.camelToSpaces(taskName || this.commandDto.commandName!);
        }
    }

    private getServiceAllocationId() {
        if ((<CommandDtoServer> this.commandDto).latestServiceAllocationId !== undefined) {
            return (<CommandDtoServer> this.commandDto).latestServiceAllocationId;
        }
        return null;
    }

    /**
     * taskName is used to differentiate the task-workflow like tasks into ReferralUpdateCommandDto.
     * However, taskName is used in a few places, like GoalUpdateCommandDto, but its not supplied so
     * doesn't match here. However it is supplied in the command itself but the command is only
     * generated client side - after that the viewModel and dto are used in transferring commands.
     * We can make use of the more accurate commandName property - but its not always defined if
     * the dto is generated from older client-side commands (its populated from the server).
     */
    // NB a service recipient / referral should never change its serviceId because the taskName is reliant
    // on serviceId not changing for a referral - else task names can change if they have been shared
    private getTaskName() {
        // this will capture any '.taskName'
        if ((<ServiceRecipientTaskBaseCommandDto> (this.commandDto as unknown)).taskName !== undefined) {
            return translateTaskName((<ServiceRecipientTaskBaseCommandDto> (this.commandDto as unknown)).taskName);
        }
        // possibly on the server, but not populated? (search latestServiceId)
        if ((<CommandDtoServer> this.commandDto).taskName !== undefined) {
            return translateTaskName((<CommandDtoServer> this.commandDto).taskName);
        }
        return null;
    }

    protected getTitleForRender(): $.JQuery {
        return $("<span>").text(this.getTitleForCommand());
    }

    public render(header: HTMLElement, child: HTMLElement) {
        let $list = $("<dl>").addClass("dl-horizontal");
        const $title = $("<div>").addClass("top-gap-15")
            .append(this.getTitleForRender())
            .append($list);
        $(child).append($title);

        const dto = this.getCommand();
        this.fieldRenderer($list, dto);
        if (adminModeEnabled()) {
            $(child).append($("<div>").css("word-wrap", "break-word").text(JSON.stringify(this.commandDto)));
        }
    }

    protected fieldRenderer($list: $.JQuery, dto: T) {
        for (let field in dto) {
            var value = dto[field];
            if (value !== undefined && value != null && fieldRenderers[field]) {
                fieldRenderers[field]($list, field, <DtoFieldValue><any>value, dto);
            }
        }
    }

    protected addChangeItem($list: $.JQuery, field: string, change: StringChangeOptional) {
        if (change) {
            stringChangeRenderer($list, field, change);
        }
    }

    protected addTimestampChangeItem($list: $.JQuery, field: string, change: StringChangeOptional) {
        if (change) {
            dateTimeOmitMidnightChangeRenderer($list, field, change);
        }
    }

}

class SimpleCommandViewHandlerControl<T extends commandDtos.CommandDto> extends BaseCommandViewHandlerControl<T> {

    static isUpdateableDto(cmd: commandDtos.CommandDto): cmd is UpdateCommandDto {
        return (<UpdateCommandDto>cmd).operation !== undefined;
    }

    constructor(private title: string, sessionData: SessionData, commandDto: T) {
        super(sessionData, commandDto);
    }

    public override getTitleForCommand(): string {
        // threat / support = evidenceGroup
        if (SimpleCommandViewHandlerControl.isUpdateableDto(this.commandDto)) {
            return this.commandDto.operation == "remove" ? `${this.title} [delete]` : `${this.title} ${this.commandDto.operation}`;
        } else {
            return this.title;
        }
    }

}

class RawCommandViewHandlerControl extends BaseCommandViewHandlerControl<any> {

    public override getTitleForCommand(): string {
        return "-undefined-";
    }

    protected override getTitleForRender(): $.JQuery {
        // as per CommentCommandViewHandlerControl
        if (adminModeEnabled()) {
            const $uuid = $("<span>").text("undefined for " + this.commandDto.uuid);
            const $url = $("<span>").text(" at " + this.commandDto.commandUri).addClass("uuid");
            $url.hide();
            return $uuid.append($url);
        }
        return null;
    }
}

class AppointmentActionCommandViewHandlerControl extends BaseCommandViewHandlerControl<AppointmentActionCommandDto | AppointmentRecurringActionCommandDto> {

    public override getTitleForCommand(): string {
        let name = "rota " + this.commandDto.operation;
        if (this.commandDto.commandName == AppointmentRecurringActionCommand.discriminator) {
            name.concat(" recurring");
        }
        return name;
    }

}

class AddressHistoryCommandViewHandlerControl extends BaseCommandViewHandlerControl<AddressHistoryCommandDto> {

    public override getTitleForCommand(): string {
        return this.commandDto.operation == "add" ? "address change" : "address history [delete]";
    }

}

class FormEvidenceCommandViewHandlerControl extends BaseCommandViewHandlerControl<FormEvidenceCommandDto> {

    protected override getTitleForRender(): $.JQuery {
        // as per CommentCommandViewHandlerControl
        const $uuid = $("<div>").addClass("uuid")
            .append($("<span>").css('font-weight', 'bold').text("work uuid "))
            .append($("<span>").text(this.commandDto.workUuid));
        $uuid.hide();
        return super.getTitleForRender().prepend($uuid);
    }

    public getSameTimeUuid() {
        return this.commandDto.workUuid;
    };

    protected override fieldRenderer($listIn: $.JQuery, dto: FormEvidenceCommandDto) {
        this.addTimestampChangeItem($listIn, "work date", dto.workDate);

        // FormUpdateCommandViewModel can just change the date, so check we have something
        if (dto.jsonPatch) {
            // json patch, eg:
            // [
            //     { "op": "replace", "path": "/baz", "value": "boo" },
            //     { "op": "add", "path": "/hello", "value": ["world"] },
            //     { "op": "remove", "path": "/foo" }
            // ]
            // other operations include “move”, “copy” and “test”

            let $list = $("<dl>").addClass("dl-horizontal");
            const $url = `${baseURI}form-snapshot/svcrec/${dto.serviceRecipientId}?taskName=${dto.taskName}&workUuid=${dto.workUuid}`;

            const $link = $("<a>").attr({"href": $url, "target": "_blank"}).text("form contents");
            const $task = $("<div>").addClass("top-gap-15")
                .append($("<i>").addClass("fa fa-file-code-o")) // fa-clone a possibility also, or sticky note
                .append($link)
                .append($list);
            $listIn.append($task);

            // displaying:
            // could be raw: append($("<pre>").text(JSON.stringify(commandDto)));
            // can't really be 'fieldRendereres' as we don't have the previous value in a jsonPatch - just what it is 'to'
            // so we opt for using the specific format
            class PatchFormat {
                op : string;
                path: string;
                value?: string;
            }
            const patchLines = dto.jsonPatch as PatchFormat[];
            patchLines.forEach(p => {
                if (p.op && "add" == p.op.toLowerCase()) {
                    if (p.value) {
                        appendChange($list, toWords(p.path), null, JSON.stringify(p.value, null, 2));
                    }
                } else if (p.op && "replace" == p.op.toLowerCase() && p.value) {
                    appendChange($list, toWords(p.path), null, JSON.stringify(p.value, null, 2).concat(" [changed]"));
                } else if (p.op && "remove" == p.op.toLowerCase()) {
                    appendChange($list, toWords(p.path), null, "[removed]");
                } else {
                    appendChange($list, toWords(p.path), null, (p.value ? p.value : "").concat(" [?]"));
                }
            });
        }
    }
}

class CalendarEntryCommandViewHandler extends BaseCommandViewHandlerControl<ServiceRecipientCalendarEntryDto> {

    public override getTitleForCommand() {
        return "calendar";
    }

    protected override getTitleForRender(): $.JQuery {
        return super.getTitleForRender().prepend($("<i>").addClass("fa fa-calendar"));
    }

    public override render(header: HTMLElement, child: HTMLElement) {
        super.render(header, child);
        this.calendarEntryRenderer($(child), this.getCommand().calendarEntryViewModel);
    }

    private calendarEntryRenderer($list: $.JQuery, dto: CalendarEntryDto) {
        for (let field in dto) {
            if (dto[field] !== undefined && fieldRenderers[field]) {
                fieldRenderers[field]($list, field, dto[field], dto);
            }
        }
    }
}

class EmailCommandViewHandlerControl extends BaseCommandViewHandlerControl<SendEmailCommandDto> {

    public override getTitleForCommand() {
        return "email";
    }
    protected override getTitleForRender(): $.JQuery {
        return super.getTitleForRender().prepend($("<i>").addClass("fa fa-envelope-o"));
    }

}

class CommentCommandViewHandlerControl extends BaseCommandViewHandlerControl<WorkEvidenceCommandDto> {

    public override getTitleForCommand(): string {
        const type = EvidenceGroup.threat.name == this.commandDto.evidenceGroup
            ? "risk - comment"
            : "support - comment";
        return this.commandDto.operation == "add" ? type : type.concat(" [edit]");
    }
    public getSameTimeUuid() {
        return this.commandDto.operation == "add" ? this.commandDto.workUuid : this.commandDto.uuid; // don't have edits in the same item
    };

    protected override getTitleForRender(): $.JQuery {
        const $uuid = $("<div>").addClass("uuid")
        .append($("<span>").css('font-weight', 'bold').text("work uuid "))
        .append($("<span>").text(this.commandDto.workUuid));
        $uuid.hide();
        return super.getTitleForRender().prepend($uuid);
    }

    protected override fieldRenderer($listIn: $.JQuery, dto: WorkEvidenceCommandDto) {
        super.fieldRenderer($listIn, dto);

        if (this.commandDto.addedThreatFlags || this.commandDto.removedThreatFlags) {
            let $list = $("<dl>").addClass("dl-horizontal");
            const $task = $("<div>").addClass("top-gap-15")
                .append($("<i>").addClass("fa fa-flag"))
                .append(" risk flags")
                // .append($("<li>")
                //     .append($("<pre>").text(JSON.stringify(commandDto)) ) )
                .append($list);
            $listIn.append($task);

            /* new flags handled by super.renderer
            const hasFlags = this.commandDto.addedThreatFlags || this.commandDto.removedThreatFlags || this.commandDto.flagIds;
            if (this.commandDto.flagIds) {
                this.commandDto.flagIds.added.forEach(flagId => {
                    const flagName = this.sessionData.getListDefinitionEntryById(flagId).getName();
                    appendChange($list, "on", null, flagName);
                });
                this.commandDto.flagIds.removed.forEach(flagId => {
                    const flagName = this.sessionData.getListDefinitionEntryById(flagId).getName();
                    appendChange($list, "off", null, flagName);
                });
            }
            */

            if (this.commandDto.addedThreatFlags && this.commandDto.addedThreatFlags.to) {
                let addedFlagIds: string[] = StringUtils.csvToArray(this.commandDto.addedThreatFlags.to);
                addedFlagIds.forEach(flagId => {
                    const flagName = this.legacyLookupFlag(Number(flagId));
                    appendChange($list, "on", null, flagName);
                });
            }
            if (this.commandDto.removedThreatFlags && this.commandDto.removedThreatFlags.to) {
                let removedFlagIds: string[] = StringUtils.csvToArray(this.commandDto.removedThreatFlags.to);
                removedFlagIds.forEach(flagId => {
                    const flagName = this.legacyLookupFlag(Number(flagId));
                    // NB swap the from/to so we get the 'del' so it crosses out the text
                    appendChange($list, "off", flagName, null);
                });
            }
        }

    }

    private legacyLookupFlag(id: number): string {
        // we no longer have reference to the flags table, although it still exists
        return id.toString();
    }

}

class RiskAreaCommandViewHandlerControl extends BaseCommandViewHandlerControl<AreaUpdateCommandDto> {

    public override getTitleForCommand(): string {
        return "risk area";
    }
    public getSameTimeUuid() {
        return this.commandDto.workUuid;
    };

    protected override getTitleForRender(): $.JQuery {
        return super.getTitleForRender().prepend($("<i>").addClass("fa fa-warning"));
    }

    protected override fieldRenderer($listIn: $.JQuery, dto: AreaUpdateCommandDto) {
        if (dto.levelChange && (dto.levelChange.to != null)) {
            const fromNum = dto.levelChange.from != null ? dto.levelChange.from : -1;
            const fromRag = redAmberGreens.filter(rag => rag.value == fromNum)[0];
            const toRag = redAmberGreens.filter(rag => rag.value == dto.levelChange!.to)[0];
            // assume rag if found in the array - "showOutcomeComponents", "rag"
            if (fromRag && toRag) {
                appendChange($listIn, "level change", fromRag.label, toRag.label);
                // if not rag - could be 5-level rating
            } else {
                appendChange($listIn, "level change", dto.levelChange.from, dto.levelChange.to);
            }
        }

        super.fieldRenderer($listIn, dto);
    }
}

//
class GoalCommandViewHandlerControl extends BaseCommandViewHandlerControl<GoalUpdateCommandDto> {

    private regex = /evidence\/(\w+)/;

    public override getTitleForCommand(): string {
        // GoalUpdateCommand saves as GoalUpdateCommandDto but doesn't save the taskName in the body, nor does it extend
        // a command with taskName - so the taskName on GoalUpdateCommandDto.super is a catch all and shouldn't really be there.
        // We need to have taskName, but the historical commands mean we are better extracting this, eg:
        //      service-recipients/200063/evidence/needs/needsReduction/goals/116/
        // or we can use the GoalCommandExtractJsonBody to add this property - but it would be good to sort the hierarchies.
        // NB other usages are: serviceRecipient.configResolver.getServiceType().lookupEvidenceTaskName(dto.taskName)
        const matches = this.commandDto.commandUri.match(this.regex)!;
        return EvidenceGroup.threat.name == matches[1]
            ? "risk - goal"
            : "support - goal";
    }
    public getSameTimeUuid() {
        return this.commandDto.workUuid;
    };

    public override render(header: HTMLElement, child: HTMLElement) {
        $(child).append(this.getAction());
        this.commandDto.goalNameChange && this.addChangeItem($(child), "description", this.commandDto.goalNameChange);
        if (this.commandDto.annotationChange) {
            this.addChangeItem($(header), "comment", this.commandDto.annotationChange["statusChangeComment"]);
        }
    }

    private getAction() {
        const $action = $("<div>");
        const goalName = (this.commandDto.goalNameChange && this.commandDto.actionInstanceUuid) ? this.commandDto.goalNameChange.to || null : null;
        const actionName = this.commandDto.actionDefId
            && this.describeAction(this.commandDto.actionDefId);
        const title = StringUtils.trimText(goalName || actionName || "-", MAX_TITLE_LEN)!;

        const $title = $("<span>").text(title);
        if (this.commandDto.statusChange && (this.commandDto.statusChange.to != this.commandDto.statusChange.from
            || this.commandDto.forceStatusChange)) {

            if (this.commandDto.statusChange.to == SmartStepStatus.AchievedAndStillRelevant
                && this.commandDto.statusChangeReason) {
                let statusChangeReasonIconClass = this.sessionData
                    .getListDefinitionEntryById(this.commandDto.statusChangeReason.to).getIconClasses();
                $action.append( $("<i>")
                    .addClass("fa selected")
                    .addClass(statusChangeReasonIconClass) );
            }
            else {
                $action.append( $("<span>")
                                    .attr("role", "img")
                                    .addClass(statusMessages[this.commandDto.statusChange.to].iconPath)
                                    .addClass("status-image")
                    .attr("title", statusMessages[this.commandDto.statusChange.to].title) );
            }
        }
        else if (this.commandDto.targetDateChange) {
            $action.append( $("<span>")
                .attr("role", "img")
                .addClass("datepicker")
                .addClass("status-image")
                .attr("title", "target date change") );
            $title.append(" [target date: " + (this.commandDto.targetDateChange.to ? EccoDate.parseIso8601(this.commandDto.targetDateChange.to).formatShort() : "removed") + "]");
        }
        else if (this.commandDto.isRelevant) {
            $action.append( $("<span>")
                .attr("role", "img")
                .addClass("status-image")
                .addClass("linked-evidence")
                .addClass("link")
                .attr("title", "relevant") );
        }

        if (!this.commandDto.targetDateChange && (this.commandDto.targetScheduleChange)) {
            $action.append( $("<span>")
                .attr("role", "img")
                .addClass("datepicker")
                .addClass("status-image")
                .attr("title", "schedule change") );
        }
        // TODO: This probably won't look pretty - but we avoid double img by checking other changes
        if (this.commandDto.targetScheduleChange) {
            $title.append(" [schedule: " + (this.commandDto.targetScheduleChange.to
                ? this.commandDto.targetScheduleChange.to : "removed") + "]");
        }
        $action.append($title);
        return $action;
    }

    protected describeAction(actionId: number) {
        try {
            return this.sessionData.describeSupportAction(actionId);
        } catch (e) {
            return this.sessionData.describeRiskAction(actionId);
        }
    }

}

class QuestionAnswerCommandViewHandlerControl extends BaseCommandViewHandlerControl<QuestionAnswerCommandDto> {

    public override getTitleForCommand(): string {
        return "questionnaire";
    }
    public getSameTimeUuid() {
        return this.commandDto.workUuid;
    };

    public override render(header: HTMLElement, child: HTMLElement) {
        let $list = $("<dl>").addClass("dl-horizontal");
        const $task = $("<div>").addClass("top-gap-15")
            .append($("<i>").addClass("fa fa-question"))
            .append(" question response")
            .append($list);
        $(child).append($task);
        this.answerChangeRenderer($list, null, null, this.commandDto);
    }

    private lookupAnswerDisplayValue(id: number, answerValue: string): string {
        return answerValue && this.sessionData
            .getAnswerDisplayValue(id, answerValue);
    }

    //noinspection JSUnusedLocalSymbols
    private answerChangeRenderer($list: $.JQuery, field: string, change: any, dto: QuestionAnswerCommandDto) {
        const key = this.sessionData.lookupQuestion(dto.questionDefId);
        const from = dto.answerChange ? this.lookupAnswerDisplayValue(dto.questionDefId, dto.answerChange.from) : "";
        const to = dto.answerChange ? this.lookupAnswerDisplayValue(dto.questionDefId, dto.answerChange.to) : "";

        appendChange($list, key, from, to);
    }

}

class SignCommandViewHandlerControl extends BaseCommandViewHandlerControl<SignWorkCommandDto> {

    public override getTitleForCommand(): string {
        return "signature";
    }
    public getSameTimeUuid() {
        // if we've signed one thing - then join it
        // if we've signed many things, it lives on its own
        // NB they aren't necessarily signed at the same time! agreements etc can be later, but the audit time is there still
        // NB and the chances are its loaded in the same page because its the same time
        return this.commandDto.workUuids && this.commandDto.workUuids.length == 1
            ? this.commandDto.workUuids[0]
            : null;
    };

    public override render(header: HTMLElement, child: HTMLElement) {
        const $uuid = $("<div>").addClass("uuid")
            .append($("<span>").css('font-weight', 'bold').text("work uuid "))
            .append($("<span>").text(this.commandDto.workUuids.join(",")));
        $uuid.hide();
        $(header).append($uuid);

        let $list = $("<dl>").addClass("dl-horizontal");

        const $task = $("<div>").addClass("top-gap-15")
            .append($("<i>").addClass("fa fa-pencil"))
            .append(" sign work")
            .append($list);
        $(child).append($task);

        for (let field in this.commandDto) {
            if (this.commandDto[field] !== undefined && fieldRenderers[field]) {
                fieldRenderers[field]($list, field, this.commandDto[field], this.commandDto);
            }
        }
    }
}

class ReferralCommandViewHandlerControl extends BaseCommandViewHandlerControl<ServiceRecipientTaskBaseCommandDto> {

    public override getTitleForCommand(): string {
        return this.commandDto.taskName !== undefined
            ? "task update: " + super.getTitleForCommand()
            : " -undefined- ";
    }

}

function getEvidenceCommentTitle(evidenceGroup: string): string {
    return EvidenceGroup.threat.name == evidenceGroup
        ? "risk - comment"
        : "support - comment";
}

abstract class DeleteEvidenceBaseCommandViewHandlerControl<T extends BaseServiceRecipientCommandDto> extends BaseCommandViewHandlerControl<T> {

    static isDeleteDto(cmd: BaseServiceRecipientCommandDto): cmd is DeleteEvidenceCommandDto {
        return (<DeleteEvidenceCommandDto>cmd).requestDeletionUuid !== undefined;
    }

    public override getTitleForCommand(): string {
        // threat / support = evidenceGroup
        if (DeleteEvidenceBaseCommandViewHandlerControl.isDeleteDto(this.commandDto)) {
            return getEvidenceCommentTitle(this.commandDto.evidenceGroup).concat(" [deleted]")
        } else {
            const cmd = this.commandDto as any as DeleteEvidenceRequestCommandDto;
            return getEvidenceCommentTitle(cmd.evidenceGroup).concat(" [delete request]");
        }
    }

protected processDeleteEvidence($child: $.JQuery, jsonViewModel: string, label: string, reason: string, crossOut: boolean) {
        let $list = $("<dl>").addClass("dl-horizontal");
        const $task = $("<div>").addClass("top-gap-15")
            .append($("<i>").addClass("fa fa-trash-o"))
            .append(" ".concat(label))
            .append($("<div>").text(reason))
            .append($list);
        $child.append($task);
        // TODO better as WorkEvidenceCommandDto
        const domObj = <BaseOutcomeBasedWork>(JSON.parse(jsonViewModel));
        this.evidenceRenderer($list, domObj, crossOut);
    }

    /** Render the evidence.
     * NB Would be nice to re-use BaseHistoryItemControl - if we could reliably determine
     * the command is SupportWork or RiskWorkEvidenceDto */
    private evidenceRenderer($list, dto: BaseOutcomeBasedWork, crossOut: boolean) {
        let workDateTime = EccoDateTime.parseIso8601(dto.workDate);
        let clazz = crossOut ? "<del>" : "";
        $list.append($("<dt>").text(workDateTime.formatDateTimePretty(true)))
            .append($(clazz).text(dto.comment));
    }
}

class DeleteEvidenceRequestCommandViewHandlerControl extends DeleteEvidenceBaseCommandViewHandlerControl<DeleteEvidenceRequestCommandDto> {

    public override render(header: HTMLElement, child: HTMLElement) {
        const labelDelReq = this.commandDto.revoke ? "delete evidence revoke" : "delete evidence request";
        this.processDeleteEvidence($(child), this.commandDto.jsonViewModel, labelDelReq, this.commandDto.reason, !this.commandDto.revoke);
    }

}

class DeleteEvidenceCommandViewHandlerControl extends DeleteEvidenceBaseCommandViewHandlerControl<DeleteEvidenceCommandDto> {

    public override render(header: HTMLElement, child: HTMLElement) {
        const labelDel = "delete evidence";
        this.processDeleteEvidence($(child), this.commandDto.jsonViewModel, labelDel, this.commandDto.reason, true);
    }

}


class CreateReferralCommandViewHandlerControl extends BaseCommandViewHandlerControl<CreateReferralCommandDto> {

    public override getTitleForCommand(): string {
        return "file [created]";
    }

    public override render(header: HTMLElement, child: HTMLElement) {
        super.render(header, child);
        const dto = this.commandDto;
        // test one property that gets populated when using reporting (ReportController.java), because its not there (nor needed) on individual command rendering
        // use serviceId
        if (dto.latestServiceAllocationId) {
            let $list = $("<dl>").addClass("dl-horizontal");
            $list.append($("<dt>").append($("<span>").text(dto.displayName)))
                .append($("<span>").text("r-id ".concat(dto.latestReferralCode || dto.latestReferralId.toString()).concat(" [c-id ").concat(dto.latestClientCode).concat("] ")))
                .append($("<span>").text(this.sessionData.getServiceCategorisationName(dto.latestServiceAllocationId)))
                .append($("<span>").text("r-id ".concat(dto.latestReferralId.toString())).hide());
            $(child).append($list);
        }
    }

}

interface ReferralRender {
    serviceRecipientId: number;
    clientCode: string;
    referralCode: string;
    displayName: string;
    referralId: number;
    clientId: number;
    serviceAllocationId: number;
}

abstract class DeleteServiceRecipientBaseCommandViewHandlerControl<T extends BaseServiceRecipientCommandDto> extends BaseCommandViewHandlerControl<T> {

    static isDeleteDto(cmd: BaseServiceRecipientCommandDto): cmd is DeleteServiceRecipientCommandDto {
        return (<DeleteServiceRecipientCommandDto>cmd).requestDeletionUuid !== undefined;
    }

    public override getTitleForCommand(): string {
        if (DeleteServiceRecipientBaseCommandViewHandlerControl.isDeleteDto(this.commandDto)) {
            return "file [deleted]";
        } else {
            const cmd = this.commandDto as any as DeleteRequestServiceRecipientCommandDto;
            return "file [delete request]";
        }
    }

protected processDeleteFile($child: $.JQuery, referral: ReferralRender, label: string, reason: string, crossOut: boolean) {
        let $list = $("<dl>").addClass("dl-horizontal");
        const $task = $("<div>").addClass("top-gap-15")
            .append($("<i>").addClass("fa fa-trash-o"))
            .append(" ".concat(label))
            .append($("<div>").text(reason))
            .append($list);
        $child.append($task);
        this.renderDeleteFile($list, referral, crossOut);
    }

    /** Render the file details. */
    private renderDeleteFile($list, dto: ReferralRender, crossOut: boolean) {

        // test one property that gets populated when using reporting (ReportController.java), because its not there (nor needed) on individual command rendering
        // use serviceId
        if (dto.serviceAllocationId) {
            let clazz = crossOut ? "<del>" : "";
            $list.append($("<dt>").append($(clazz).text(dto.displayName)))
                .append($(clazz).text("r-id ".concat(dto.referralCode || dto.referralId.toString()).concat(" [c-id ").concat(dto.clientCode).concat("] ")))
                .append($(clazz).text(this.sessionData.getServiceCategorisationName(dto.serviceAllocationId)))
                .append($("<span>").text("sr-id ".concat(dto.serviceRecipientId.toString())).hide());
        }
    }
}

class DeleteRequestServiceRecipientCommandViewHandlerControl extends DeleteServiceRecipientBaseCommandViewHandlerControl<DeleteRequestServiceRecipientCommandDto> {

    public override render(header: HTMLElement, child: HTMLElement) {
        const labelDelReq = this.commandDto.revoke ? "delete file revoke" : "delete file request";
        const referral: ReferralRender = {
            displayName: this.commandDto.displayName,
            serviceRecipientId: this.commandDto.serviceRecipientId,
            referralCode: this.commandDto.latestReferralCode,
            clientCode: this.commandDto.latestClientCode,
            referralId: this.commandDto.latestReferralId,
            clientId: this.commandDto.latestClientId,
            serviceAllocationId: this.commandDto.latestServiceAllocationId,
        };
        this.processDeleteFile($(child), referral, labelDelReq, this.commandDto.reason, !this.commandDto.revoke);
    }

}

class DeleteServiceRecipientCommandViewHandlerControl extends DeleteServiceRecipientBaseCommandViewHandlerControl<DeleteServiceRecipientCommandDto> {

    public override render(header: HTMLElement, child: HTMLElement) {
        const labelDel = "delete file";
        // Referral may not match all the properties over time - but we go with it for now
        // The jsonViewModel is a dto.Referral saved from SoftDeleteCommand.ts, deleteReferralSelected
        const domObj = <ReferralDto>(JSON.parse(this.commandDto.jsonViewModel));
        this.processDeleteFile($(child), domObj as any as ReferralRender, labelDel, this.commandDto.reason, true);
    }
}

class UserAccessAuditCommandViewHandler extends BaseCommandViewHandlerControl<UserAccessAuditCommandDto> {

    public override getTitleForCommand() {
        const level = this.getCommand().level;
        const levelDesc = level == "STATUS" ? "summary" : level == "SHORT_HISTORY" ? "brief history" : "full history";
        return `user access of ${super.getTitleForCommand()} - ${levelDesc}`;
    }

    protected override getTitleForRender(): $.JQuery {
        return super.getTitleForRender().prepend($("<i>").addClass("fa fa-user-secret").append("&nbsp;"));
    }

    public override render(header: HTMLElement, child: HTMLElement) {
        super.render(header, child);
        this.localRenderer($(child), this.getCommand());
    }

    private localRenderer($list: $.JQuery, dto: UserAccessAuditCommandDto) {
        for (let field in dto) {
            if (dto[field] !== undefined && fieldRenderers[field]) {
                fieldRenderers[field]($list, field, dto[field], dto);
            }
        }
    }
}


class UserCoreCommandViewHandlerControl extends BaseCommandViewHandlerControl<UserChangeDto> {

    public override getTitleForCommand() {
        return "";
    }

    protected override getTitleForRender(): $.JQuery {
        return super.getTitleForRender().prepend($("<i>").addClass("fa fa-user").append("&nbsp;"));
    }

}


class UserMfaResetCommandViewHandlerControl extends BaseCommandViewHandlerControl<UserMfaResetCommandDto> {

    public override getTitleForCommand() {
        return "reset mfa secret";
    }

    protected override getTitleForRender(): $.JQuery {
        return super.getTitleForRender().prepend($("<i>").addClass("fa fa-user").append("&nbsp;"));
    }

}

class GroupActivityCommandViewHandler extends BaseCommandViewHandlerControl<GroupActivityCommandDto> {

    public override getTitleForCommand() {
        return "group activity";
    }

    private isComms() {
        return this.getCommand().discriminator_orm == GroupActivityCommand.DISCRIMINATOR_COMMS;
    }

    protected override getTitleForRender(): $.JQuery {
        // this only shows COMMS audits for CREATE - update/delete are not differentiated in this quick shared usage
        const icon = this.isComms() ? "fa-comments-o" : "fa-group";
        return super.getTitleForRender().prepend($("<i>").addClass("fa " + icon));
    }

    protected override fieldRenderer($list: $.JQuery, dto: GroupActivityCommandDto) {
        if (!this.isComms()) {
            super.fieldRenderer($list, dto);
        }
        // switch names
        for (let field in dto) {
            var value = dto[field];
            if (value !== undefined && value != null && fieldRenderers[field]) {
                fieldRenderers[field]($list, field, <DtoFieldValue><any>value, dto);
            }
        }
    }

}
